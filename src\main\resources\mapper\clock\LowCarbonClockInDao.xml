<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.clock.dao.LowCarbonClockInDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.clock.entity.LowCarbonClockIn">
    <!--@mbg.generated-->
    <!--@Table gl_low_carbon_clock_in-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="tip_message" jdbcType="VARCHAR" property="tipMessage" />
    <result column="ai_tip" jdbcType="VARCHAR" property="aiTip" />
    <result column="emission" jdbcType="DECIMAL" property="emission" />
    <result column="gap_time" jdbcType="INTEGER" property="gapTime" />
    <result column="sequence" jdbcType="INTEGER" property="sequence" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="updated" jdbcType="TIMESTAMP" property="updated" />
    <result column="updator" jdbcType="BIGINT" property="updator" />
    <result column="deleted" jdbcType="BOOLEAN" property="deleted" />
    <result column="state" jdbcType="INTEGER" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, icon, tip_message, ai_tip, emission, gap_time, `sequence`, created, creator, 
    updated, updator, deleted, `state`
  </sql>

  <select id="getMyClassify" resultType="com.lvpuhui.gic.wxapp.clock.dto.response.CarbonBookVo$Classify">
    SELECT
      glcci.id,
      glcci.`name`,
      glcci.icon,
      SUM( glccid.emission) AS emission
    FROM
      gl_low_carbon_clock_in glcci
        LEFT JOIN ( SELECT * FROM gl_low_carbon_clock_in_details WHERE mobile_sha256 = #{mobileSha256} ) glccid ON glcci.id = glccid.clock_in_id
    WHERE
      glcci.deleted = 0
      AND glcci.state = 1
    GROUP BY glcci.id
    ORDER BY glcci.sequence DESC
  </select>
</mapper>