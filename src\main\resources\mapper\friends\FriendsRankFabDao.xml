<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.friends.dao.FriendsRankFabDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.friends.entity.FriendsRankFab">
    <!--@mbg.generated-->
    <!--@Table gl_friends_rank_fab-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="mobile_sha256" jdbcType="VARCHAR" property="mobileSha256" />
    <result column="fab_mobile_sha256" jdbcType="VARCHAR" property="fabMobileSha256" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, version, mobile_sha256, fab_mobile_sha256, created
  </sql>

  <select id="getFriendsRank" resultType="com.lvpuhui.gic.wxapp.friends.dto.response.FriendsRankVo$FriendsRankUser">
    SELECT
      guf.friends_sha256 as mobileSha256,
      gu.id as userId,
      gu.avatar_url,
      gu.nick_name,
      IF
      ( gfrf.id > 0, 1, 0 ) AS isFab
    FROM
      gl_user_friends guf
        INNER JOIN gl_user gu ON guf.friends_sha256 = gu.mobile_sha256
        LEFT JOIN ( SELECT * FROM gl_friends_rank_fab WHERE fab_mobile_sha256 = #{mobileSha256} AND version = #{version} ) gfrf ON gfrf.mobile_sha256 = guf.friends_sha256
    WHERE
      guf.mobile_sha256 = #{mobileSha256}
  </select>

  <select id="getFabList" resultType="com.lvpuhui.gic.wxapp.friends.dto.response.FabFriendsListVo">
    SELECT
      gu.avatar_url,
      gu.nick_name,
      gfrf.created AS fabTime
    FROM
      gl_friends_rank_fab gfrf
        INNER JOIN gl_user gu ON gfrf.fab_mobile_sha256 = gu.mobile_sha256
    WHERE
      gfrf.mobile_sha256 = #{mobileSha256}
    AND gfrf.version = #{version}
  </select>

  <select id="getLikeCount" resultType="java.lang.Integer">
    SELECT COUNT(1) FROM gl_friends_rank_fab WHERE fab_mobile_sha256 = #{mobileSha256}
  </select>

  <select id="getLikedCount" resultType="int">
    SELECT COUNT(1) FROM gl_friends_rank_fab WHERE mobile_sha256 = #{mobileSha256}
  </select>
</mapper>