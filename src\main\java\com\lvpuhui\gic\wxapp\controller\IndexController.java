package com.lvpuhui.gic.wxapp.controller;


import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.entity.dto.*;
import com.lvpuhui.gic.wxapp.entity.po.*;
import com.lvpuhui.gic.wxapp.entity.vo.EmissionCertificateVo;
import com.lvpuhui.gic.wxapp.entity.vo.GreenPointsVo;
import com.lvpuhui.gic.wxapp.entity.vo.IndexEmissionVo;
import com.lvpuhui.gic.wxapp.entity.vo.UrlLinkVo;
import com.lvpuhui.gic.wxapp.infrastructure.interceptor.PassToken;
import com.lvpuhui.gic.wxapp.service.*;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 *
 * 提供面向租户的API接口服务
 * 文件上传
 * 获取FTP上传信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("")
@Slf4j
public class IndexController {

    @Autowired
    private GlBannerService glBannerService;

    @Autowired
    private GlCompanyProductService glCompanyProductService;

    @Resource
    PointsQueryService pointsQueryService;

    @Resource
    EmissionCertificateService emissionCertificateService;

    @Autowired
    private GlVideoService glVideoService;

    @Autowired
    private GlNoticeService glNoticeService;

    @Autowired
    private GlQuestionService glQuestionService;

    @Autowired
    private GlSubjectRecordService glSubjectRecordService;

    @Resource
    private GlVideoTopicService glVideoTopicService;

    @PassToken
    @RequestMapping("/emission")
    public R<IndexEmissionVo> getEmission(){
        return pointsQueryService.getEmission();
    }

    @GetMapping("/green_points2")
    public R<GreenPointsVo> greenPoints2(@RequestParam("page") Integer page){
        return R.ok(pointsQueryService.getGreenPoints2(UserUtils.getMobileSha256(),page));
    }

    @RequestMapping("/emission_certificate")
    public R<EmissionCertificateVo> emissionCertificate(String mobileSha256){
        return R.ok(emissionCertificateService.buildCertificate(mobileSha256));
    }

    /**
     * 首页轮播图列表接口-lijianguang
     */
    @PassToken
    @GetMapping("/banner")
    public R<List<Banner>> banner(){
        List<Banner> banners = glBannerService.banner();
        return R.ok(banners);
    }

    /**
     * 首页公司小程序展示列表
     */
    @PassToken
    @GetMapping("/company_list")
    public R<List<SceneCategories>> companyList(){
        List<SceneCategories> sceneCategoriesList = glCompanyProductService.companyList();
        return R.ok(sceneCategoriesList);
    }

    /**
     * 视频列表接口-lijianguang
     */
//    @PostMapping("/video_list")
    public R<List<VideoList>> videoList(@RequestBody @Valid VideoListDto videoListDto){
        List<VideoList> videoLists = glVideoService.videoList(videoListDto);
        return R.ok(videoLists);
    }

    /**
     * 视频完成接口-lijianguang
     */
//    @PostMapping("/video_finish")
    public R<VideoFinish> videoFinish(@RequestBody @Valid VideoFinishDto videoFinishDto){
        VideoFinish videoFinish = glVideoService.videoFinish(videoFinishDto);
        return R.ok(videoFinish);
    }

    /**
     * 公告列表接口-lijianguang
     */
    @PassToken
    @GetMapping("/notice_list")
    public R<List<NoticeList>> noticeList(){
        List<NoticeList> noticeLists = glNoticeService.noticeList();
        return R.ok(noticeLists);
    }

    /**
     * 公告详情接口-lijianguang
     */
    @PassToken
    @PostMapping("/notice_detail")
    public R<NoticeDetail> noticeDetail(@RequestBody @Valid NoticeDetailDto noticeDetailDto){
        NoticeDetail noticeDetail = glNoticeService.noticeDetail(noticeDetailDto);
        return R.ok(noticeDetail);
    }

    /**
     * 问卷列表接口-lijianguang
     */
    @PostMapping("/question_list")
    public R<List<QuestionList>> questionList(@RequestBody @Valid QuestionListDto questionListDto){
        List<QuestionList> questionLists = glQuestionService.questionList(questionListDto);
        return R.ok(questionLists);
    }

    /**
     * 问卷完成接口-lijianguang
     */
    @PassToken
    @PostMapping("/question_finish")
    public R<QuestionFinish> questionFinish(@RequestBody @Valid QuestionFinishDto questionFinishDto){
        QuestionFinish questionFinish = glQuestionService.questionFinish(questionFinishDto);
        return R.ok(questionFinish);
    }

    /**
     * 当天是否还有做题次数接口-lijianguang
     */
    @PostMapping("/check_subject")
    public R<SubjectNumber> checkSubject(@RequestBody @Valid SubjectNumberDto subjectNumberDto){
        SubjectNumber subjectNumber = glSubjectRecordService.checkSubject(subjectNumberDto);
        return R.ok(subjectNumber);
    }

    /**
     * 问答配置接口-lijianguang
     */
    @GetMapping("/subject_config")
    public R<SubjectConfig> subjectConfig(){
        SubjectConfig subjectConfig = glSubjectRecordService.subjectConfig();
        return R.ok(subjectConfig);
    }

    /**
     * 问答完成接口-lijianguang
     */
    @PostMapping("/subject_finish")
    public R<SubjectFinish> subjectFinish(@RequestBody @Valid SubjectFinishDto subjectFinishDto){
        SubjectFinish subjectFinish = glSubjectRecordService.subjectFinish(subjectFinishDto);
        return R.ok(subjectFinish);
    }

    /**
     * 视频主题列表接口-lijianguang
     */
//    @GetMapping("/video_topic_list")
    public R<List<VideoTopic>> videoTopicList(){
        List<VideoTopic> videoTopics = glVideoTopicService.videoTopicList();
        return R.ok(videoTopics);
    }

    /**
     * 获取微信短链
     */
    @PassToken
    @GetMapping("/url_link")
    public R<UrlLinkVo> getUrlLink(){
        UrlLinkVo urlLinkVo = glVideoTopicService.getUrlLink();
        return R.ok(urlLinkVo);
    }
}
