<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.team.dao.GlTeamMemberRankDao">

    <select id="selectTeamRankMembers" resultType="com.lvpuhui.gic.wxapp.team.dto.TeamListDetailMemberVo">
        SELECT
            gu.nick_name,
            gu.avatar_url,
            gtmr.emission
        FROM
            gl_team_member_rank gtmr
                INNER JOIN gl_user gu ON gtmr.user_id = gu.id
        WHERE gtmr.team_id = #{teamId}
        ORDER BY
            gtmr.emission DESC
            LIMIT 5
    </select>
    <select id="selectEmissionUserCount" resultType="com.lvpuhui.gic.wxapp.team.dto.TeamAvgEmissionUserCountDto">
        SELECT
            COUNT( user_id ) AS userCount,
            AVG( emission ) AS avgEmission
        FROM
            gl_team_member_rank
        WHERE
            team_id = #{teamId}
          AND
            emission > 0 AND
            emission IS NOT NULL
    </select>
    <select id="selectTeamMemberRanks" resultType="com.lvpuhui.gic.wxapp.team.dto.TeamMemberRankListVo">
        SELECT
            gtmr.team_id,
            gtmr.user_id,
            gtmr.`rank`,
            gtmr.emission,
            gu.nick_name,
            gu.avatar_url
        FROM
            gl_team_member_rank gtmr
                INNER JOIN gl_user gu ON gtmr.user_id = gu.id
        WHERE gtmr.team_id = #{teamId}
        ORDER BY
            -gtmr.`rank` DESC
            LIMIT 20
    </select>
    <select id="selectTeamMemberRankByUserId" resultType="com.lvpuhui.gic.wxapp.team.dto.TeamMemberRankListVo">
        SELECT
            gtmr.team_id,
            gtmr.user_id,
            gtmr.`rank`,
            gtmr.emission,
            gu.nick_name,
            gu.avatar_url
        FROM
            gl_user gu
                INNER JOIN gl_team_member_rank gtmr ON gu.id = gtmr.user_id
        WHERE gu.id = #{userId} AND gtmr.team_id = #{teamId}
    </select>
</mapper>

