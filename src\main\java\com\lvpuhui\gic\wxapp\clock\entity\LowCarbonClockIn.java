package com.lvpuhui.gic.wxapp.clock.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 低碳打卡表
 */
@Schema(description = "低碳打卡表")
@Getter
@Setter
@TableName(value = "gl_low_carbon_clock_in")
public class LowCarbonClockIn {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键")
    @NotNull(message = "主键不能为null")
    private Long id;

    /**
     * 打卡事项名称
     */
    @TableField(value = "`name`")
    @Schema(description = "打卡事项名称")
    @Size(max = 100, message = "打卡事项名称最大长度要小于 100")
    private String name;

    /**
     * 打卡事项icon
     */
    @TableField(value = "icon")
    @Schema(description = "打卡事项icon")
    @Size(max = 255, message = "打卡事项icon最大长度要小于 255")
    private String icon;

    /**
     * 打卡提示信息
     */
    @TableField(value = "tip_message")
    @Schema(description = "打卡提示信息")
    @Size(max = 100, message = "打卡提示信息最大长度要小于 100")
    private String tipMessage;

    /**
     * ai提示词
     */
    @TableField(value = "ai_tip")
    @Schema(description = "ai提示词")
    @Size(max = 255, message = "ai提示词最大长度要小于 255")
    private String aiTip;

    /**
     * 减排量(克)
     */
    @TableField(value = "emission")
    @Schema(description = "减排量(克)")
    private BigDecimal emission;

    /**
     * 间隔时间(小时)
     */
    @TableField(value = "gap_time")
    @Schema(description = "间隔时间(小时)")
    private Integer gapTime;

    /**
     * 位置权重
     */
    @TableField(value = "`sequence`")
    @Schema(description = "位置权重")
    private Integer sequence;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description = "创建时间")
    private Date created;

    /**
     * 创建者ID
     */
    @TableField(value = "creator")
    @Schema(description = "创建者ID")
    private Long creator;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    @Schema(description = "更新时间")
    private Date updated;

    /**
     * 修改者ID
     */
    @TableField(value = "updator")
    @Schema(description = "修改者ID")
    private Long updator;

    /**
     * 删除 0未删除 1已删除
     */
    @TableField(value = "deleted")
    @Schema(description = "删除 0未删除 1已删除")
    private Integer deleted;

    /**
     * 状态 0:保存 1:发布
     */
    @TableField(value = "`state`")
    @Schema(description = "状态 0:保存 1:发布")
    private Integer state;
}