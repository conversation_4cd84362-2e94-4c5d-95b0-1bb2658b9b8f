package com.lvpuhui.gic.wxapp.controller;

import com.baomidou.mybatisplus.extension.api.ApiController;
import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.entity.dto.CheckTickDto;
import com.lvpuhui.gic.wxapp.entity.dto.TickoffDto;
import com.lvpuhui.gic.wxapp.entity.dto.TicksDto;
import com.lvpuhui.gic.wxapp.entity.po.CheckTick;
import com.lvpuhui.gic.wxapp.entity.po.Tickoff;
import com.lvpuhui.gic.wxapp.entity.po.Ticks;
import com.lvpuhui.gic.wxapp.entity.po.TicksDetail;
import com.lvpuhui.gic.wxapp.service.GlTickoffService;
import com.lvpuhui.gic.wxapp.service.GlTickoffTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 打卡服务
 * <AUTHOR>
 * @since 2022年05月06日 18:35:00
 */
@RestController
public class GlTickoffController extends ApiController {

    @Autowired
    private GlTickoffTypeService glTickoffTypeService;

    @Autowired
    private GlTickoffService glTickoffService;

    /**
     * 打卡列表(每日打卡、新人签到等)接口-lijianguang
     */
    @GetMapping("/ticks")
    public R<List<Ticks>> ticks(@RequestParam("mobileSha256") String mobileSha256){
        TicksDto ticksDto = new TicksDto();
        ticksDto.setMobileSha256(mobileSha256);
        List<Ticks> ticks = glTickoffTypeService.ticks(ticksDto);
        return success(ticks);
    }

    /**
     * 打卡接口-lijianguang
     */
    @PostMapping("/punch_clock")
    public R<Tickoff> punchClock(@RequestBody @Valid TickoffDto tickoffDto){
        R<Tickoff> tickoffR = glTickoffService.punchClock(tickoffDto);
        return tickoffR;
    }

    /**
     * 判断是否已打卡接口-lijianguang
     */
    @PostMapping("/check_tick")
    public R<CheckTick> checkTick(@RequestBody @Valid CheckTickDto checkTickDto){
        CheckTick checkTick = glTickoffService.checkTick(checkTickDto);
        return success(checkTick);
    }

    /**
     * 打卡详情信息接口-lijianguang
     */
    @GetMapping("/ticks_detail")
    public R<TicksDetail> ticksDetail(@RequestParam("tickId") Long tickId){
        TicksDetail ticksDetail = glTickoffTypeService.ticksDetail(tickId);
        return success(ticksDetail);
    }
}
