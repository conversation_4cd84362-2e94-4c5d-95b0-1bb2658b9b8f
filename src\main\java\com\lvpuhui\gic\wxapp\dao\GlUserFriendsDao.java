package com.lvpuhui.gic.wxapp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.entity.GlUserFriends;
import com.lvpuhui.gic.wxapp.entity.vo.FriendsNumberRankVo;
import com.lvpuhui.gic.wxapp.entity.vo.RankFirstVo;
import com.lvpuhui.gic.wxapp.friends.dto.response.FriendsUserVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户好友Mapper接口
 * <AUTHOR>
 * @since 2022-07-26
 */
public interface GlUserFriendsDao extends BaseMapper<GlUserFriends> {

    int getCountByMobileSha256(@Param("mobileSha256") String mobileSha256);

    List<FriendsUserVo> getMyFriendsList(@Param("mobileSha256") String mobileSha256);

    List<FriendsNumberRankVo> friendsNumberRank();

    RankFirstVo.RankFirstUserVo getInfluenceRankFirstUser();
}