package com.lvpuhui.gic.wxapp.team.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 战队详情返回值
 * <AUTHOR>
 * @since 2023年05月24日 15:36:00
 */
@Data
public class TeamListDetailVo {

    /**
     * 排名
     */
    private Long rank;

    /**
     * 自己战队的ID
     */
    private Long myTeamId;

    /**
     * 战队ID
     */
    private Long id;

    /**
     * 战队名称
     */
    private String teamName;

    /**
     * 战队Logo
     */
    private String teamLogo;

    /**
     * 加入方式,无限制:0,邀请:1
     */
    private Integer joinMode;

    /**
     * 密码是否为空  true：为空 false：不为空
     */
    private Boolean passwordBlank = true;

    /**
     * 成员人数
     */
    private Integer memberCount;

    /**
     * 减排量
     */
    private BigDecimal emission;

    /**
     * 减排量-文本
     */
    private String emissionText;

    /**
     * 创建人昵称
     */
    private String nickName;

    /**
     * 创建人头像
     */
    private String avatarUrl;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd")
    private LocalDateTime created;

    /**
     * 区域code
     */
    private String regionCode;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 成员信息集合
     */
    private List<TeamListDetailMemberVo> memberVos;

    public String getEmissionText() {
        if(Objects.nonNull(emission)){
            return CalcUtil.weightFormat(emission.doubleValue());
        }
        return emissionText;
    }
}
