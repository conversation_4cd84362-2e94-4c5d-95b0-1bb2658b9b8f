package com.lvpuhui.gic.wxapp.team.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 战队成员表(GlTeamMember)表实体类
 *
 * <AUTHOR>
 * @since 2023-05-24 11:24:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_team_member")
public class GlTeamMember extends Model<GlTeamMember> {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 战队ID
     */
    private Long teamId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 邀请人ID
     */
    private Long inviteUserId;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}

