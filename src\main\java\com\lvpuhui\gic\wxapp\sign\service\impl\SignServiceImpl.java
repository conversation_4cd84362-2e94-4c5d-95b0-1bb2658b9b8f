package com.lvpuhui.gic.wxapp.sign.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.dao.GlAppletConfigDao;
import com.lvpuhui.gic.wxapp.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.entity.GlAppletConfig;
import com.lvpuhui.gic.wxapp.entity.GlUser;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.service.GlPointsService;
import com.lvpuhui.gic.wxapp.sign.Exception.BizException;
import com.lvpuhui.gic.wxapp.sign.dao.*;
import com.lvpuhui.gic.wxapp.sign.dto.*;
import com.lvpuhui.gic.wxapp.sign.entity.*;
import com.lvpuhui.gic.wxapp.sign.service.SignService;
import com.lvpuhui.gic.wxapp.sign.vo.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SignServiceImpl implements SignService {

    /** 排行榜设置*/
    static final String CONFIG_TRIFLE_SETTINGS ="LOWCARBON_ACTION";

    @Resource
    private GlLowcarbonActionCateDao glLowcarbonActionCateDao;

    @Resource
    private GlLowcarbonActionDao glLowcarbonActionDao;

    @Resource
    private GlLowcarbonActionExecSignDao glLowcarbonActionExecSignDao;

    @Resource
    private GlLowcarbonActionSignDetailDao glLowcarbonActionSignDetailDao;

    @Resource
    private GlAppletConfigDao glAppletConfigDao;

    @Resource
    private GlLowcarbonActionSignDao glLowcarbonActionSignDao;

    @Resource
    private GlLowcarbonActionRewardDao glLowcarbonActionRewardDao;

    @Resource
    private GlLowcarbonActionRewardTaskDao glLowcarbonActionRewardTaskDao;

    @Resource
    private GlPointsService glPointsService;

    @Resource
    private GlUserDao glUserDao;

    @Resource
    private GlAppletConfigService glAppletConfigService;




    @Override
    public SignClassifyTrifleListVo signClassIfyTrifle() {
        //查询图片前缀
        String imagePrefix = glAppletConfigService.getImagePrefix();
        SignClassifyTrifleListVo signClassifyTrifleListVo = new SignClassifyTrifleListVo();
        Map<Integer,List<TrifleVo>> map = new HashMap<>();
        Long userId = UserUtils.getUserId();
        LocalDate today = LocalDate.now();
        List<GlLowcarbonActionCateDO> list = glLowcarbonActionCateDao.signClassIfyTrifle();
        for (GlLowcarbonActionCateDO glLowcarbonActionCateDO: list) {
            List<TrifleVo> trifle = glLowcarbonActionDao.selectByCateId(glLowcarbonActionCateDO.getId());
            map.put(glLowcarbonActionCateDO.getId(), trifle);
        }
        signClassifyTrifleListVo.setMapList(map);
        //查询签到状态
        for (Map.Entry<Integer, List<TrifleVo>> entry : map.entrySet()){
            List<TrifleVo> valueList = entry.getValue();
            for (TrifleVo item: valueList) {
                String url = imagePrefix+item.getIconLight();
                item.setIconLight(url);
                GlLowcarbonActionExecSignDO glLowcarbonActionExecSignDO = glLowcarbonActionExecSignDao.getTodayData(userId,item.getId(),today);
                if(Objects.nonNull(glLowcarbonActionExecSignDO)){
                    item.setState(1);
                }else {
                    item.setState(0);
                }
            }
        }
        return signClassifyTrifleListVo;
    }

    @Override
    public Integer saveSign(SignSaveDto signSaveDto) {

        List<GlLowcarbonActionSignDetailDO> maxTotalList = glLowcarbonActionSignDetailDao.selectByUserId(UserUtils.getUserId());
        Integer everyDayPoints = 0;
        //获取今天日期
        LocalDate today = LocalDate.now();
        //获取今天到21天之前每一天的日期
        List<LocalDate> currentDaysList = new ArrayList<>();
        LocalDate endDate = today.minusDays(maxTotalList.size());
        while (!today.isBefore(endDate)) {
            today = today.minusDays(1);
            currentDaysList.add(today);
        }
        currentDaysList.add(0,LocalDate.now());
        LocalDate date = LocalDate.now();
        GlLowcarbonActionExecSignDO glLowcarbonActionExecSignDO = new GlLowcarbonActionExecSignDO();
        Long userId = UserUtils.getUserId();
        String mobieShare = UserUtils.getMobileSha256();
        //查询今天某件小事是否已经签到
        GlLowcarbonActionExecSignDO todayData = glLowcarbonActionExecSignDao.getTodayData(userId,signSaveDto.getTrifleId(),date);
        if (Objects.nonNull(todayData)){
            throw new BizException("当前小事您已签到，请勿重复");
        }
        GlAppletConfig isExit = glAppletConfigDao.selectByKey(CONFIG_TRIFLE_SETTINGS);
        ConfigDto configDto = JSON.parseObject(isExit.getParamValue(),ConfigDto.class);

        //保存小事表
        glLowcarbonActionExecSignDO.setActionId(signSaveDto.getTrifleId());
        glLowcarbonActionExecSignDO.setCreatedAt(LocalDateTime.now());
        glLowcarbonActionExecSignDO.setExecuteDate(LocalDate.now());
        glLowcarbonActionExecSignDO.setMobileSha256(mobieShare);
        glLowcarbonActionExecSignDO.setUserId(userId);
        glLowcarbonActionExecSignDao.insert(glLowcarbonActionExecSignDO);

        //查询积分表今天是否已存在
        GlLowcarbonActionSignDetailDO glLowcarbonActionSignDetailDO = glLowcarbonActionSignDetailDao.getIsExit(userId,date);
        if(Objects.isNull(glLowcarbonActionSignDetailDO)){
            GlLowcarbonActionSignDetailDO todayMessage = new GlLowcarbonActionSignDetailDO();
            todayMessage.setActionId(signSaveDto.getTrifleId());
            todayMessage.setUserId(userId);
            todayMessage.setSignDate(LocalDate.now());
            todayMessage.setCreatedAt(LocalDateTime.now());
            todayMessage.setMobileSha256(mobieShare);

            if(Objects.nonNull(configDto)){
                todayMessage.setPoint(configDto.getEveryDayPoints());
                everyDayPoints =configDto.getEveryDayPoints();
            }
            todayMessage.setType(0);
            glLowcarbonActionSignDetailDao.insert(todayMessage);


            /**
             * 保存每日积分表的同时也保存用户总积分表
             */
            if(Objects.nonNull(configDto)){
                glPointsService.supplePoints(configDto.getEveryDayPoints(),todayMessage.getId());
            }
            /**
             * 如果积分表为空，那么保存积分表的同时也要保存小事签到表的最后签到日期以及计算连续签到次数
             */
            GlLowcarbonActionSignDO glLowcarbonActionSignDO = glLowcarbonActionSignDao.getUserTrifle(userId);
            GlLowcarbonActionSignDO signDO = new GlLowcarbonActionSignDO();
            //设置比连续签到标志位
            int index = 0;
            if(Objects.nonNull(glLowcarbonActionSignDO)){
                //循环日期集合
                for (LocalDate item:currentDaysList) {
                    //查询当前日期是否签到
                    GlLowcarbonActionSignDetailDO isExitDat = glLowcarbonActionSignDetailDao.selectByUserAndLocal(item,userId);
                    if(Objects.nonNull(isExitDat)){
                        index++;
                    }else {
                        break;
                    }
                }
                signDO.setRepeatSignTotal(index);
                signDO.setUserId(userId);
                signDO.setUpdatedAt(LocalDateTime.now());
                signDO.setMobileSha256(mobieShare);
                signDO.setId(glLowcarbonActionSignDO.getId());
                signDO.setLastSignDate(LocalDate.now());
                glLowcarbonActionSignDao.updateById(signDO);
            }else {
                signDO.setLastSignDate(LocalDate.now());
                signDO.setCreatedAt(LocalDateTime.now());
                signDO.setMobileSha256(UserUtils.getMobileSha256());
                signDO.setUserId(userId);
                signDO.setRepeatSignTotal(1);
                glLowcarbonActionSignDao.insert(signDO);
            }
            /**
             * 保存任务奖励周期表
             */
            GlLowcarbonActionRewardTaskDO glLowcarbonActionRewardTaskDO = glLowcarbonActionRewardTaskDao.selectByUserId(userId);
            GlLowcarbonActionRewardTaskDO saveDate = new GlLowcarbonActionRewardTaskDO();
            if(Objects.isNull(glLowcarbonActionRewardTaskDO)){
                saveDate.setCreatedAt(LocalDateTime.now());
                saveDate.setMobileSha256(mobieShare);
                saveDate.setLastSignDate(LocalDate.now());
                saveDate.setRepeatSignTotal(1);
                saveDate.setSigned7RewardState(0);
                saveDate.setSigned14RewardState(0);
                saveDate.setSigned21RewardState(0);
                saveDate.setResetDate(LocalDate.now());
                saveDate.setUserId(userId);
                saveDate.setStartDate(LocalDate.now());
                saveDate.setLastSignDate(LocalDate.now());
                glLowcarbonActionRewardTaskDao.insert(saveDate);
            }else {
                if(index==7){
                    GlLowcarbonActionRewardDO glLowcarbonActionRewardDO = glLowcarbonActionRewardDao.selectByUserIdAndType(mobieShare);
                    if(Objects.isNull(glLowcarbonActionRewardDO)){
                        saveDate.setSigned7RewardState(1);
                        //保存奖励发放
                        GlLowcarbonActionRewardDO rewardDO = new GlLowcarbonActionRewardDO();
                        rewardDO.setMobileSha256(mobieShare);
                        rewardDO.setCreatedAt(LocalDateTime.now());
                        if(Objects.nonNull(configDto)){
                            rewardDO.setPoint(configDto.getSevenPoints());
                        }
                        rewardDO.setRewardDate(LocalDate.now());
                        rewardDO.setState(0);
                        rewardDO.setType(1);
                        glLowcarbonActionRewardDao.insert(rewardDO);
                    }
                }
                if(index==14){
                    GlLowcarbonActionRewardDO glLowcarbonActionRewardDO = glLowcarbonActionRewardDao.selectByUserIdAndfourteen(mobieShare);
                    if(Objects.isNull(glLowcarbonActionRewardDO)){
                        saveDate.setSigned14RewardState(1);
                        //保存奖励发放
                        GlLowcarbonActionRewardDO rewardDO = new GlLowcarbonActionRewardDO();
                        rewardDO.setMobileSha256(mobieShare);
                        rewardDO.setCreatedAt(LocalDateTime.now());
                        if(Objects.nonNull(configDto)) {
                            rewardDO.setPoint(configDto.getFourteenPoints());
                        }
                        rewardDO.setRewardDate(LocalDate.now());
                        rewardDO.setState(0);
                        rewardDO.setType(2);
                        glLowcarbonActionRewardDao.insert(rewardDO);
                    }
                }
                if(index==21){
                    GlLowcarbonActionRewardDO glLowcarbonActionRewardDO = glLowcarbonActionRewardDao.selectByUserIdAndTwenty(mobieShare);
                    if(Objects.isNull(glLowcarbonActionRewardDO)){
                        saveDate.setSigned21RewardState(1);
                        //保存奖励发放
                        GlLowcarbonActionRewardDO rewardDO = new GlLowcarbonActionRewardDO();
                        rewardDO.setMobileSha256(mobieShare);
                        rewardDO.setCreatedAt(LocalDateTime.now());
                        if(Objects.nonNull(configDto)) {
                            rewardDO.setPoint(configDto.getTwentyOnePoints());
                        }
                        rewardDO.setRewardDate(LocalDate.now());
                        rewardDO.setState(0);
                        rewardDO.setType(3);
                        glLowcarbonActionRewardDao.insert(rewardDO);
                    }
                }
                saveDate.setRepeatSignTotal(index);
                saveDate.setLastSignDate(LocalDate.now());
                saveDate.setUpdatedAt(LocalDateTime.now());
                saveDate.setId(glLowcarbonActionRewardTaskDO.getId());
                glLowcarbonActionRewardTaskDao.updateById(saveDate);
            }
        }
        return everyDayPoints;
    }

    @Override
    public Integer suppleSign(SuppleSignSaveDto suppleSignSaveDto) {

        String mobieShare = UserUtils.getMobileSha256();
        //查询签到积分表有多少条签到信息
        List<GlLowcarbonActionSignDetailDO> maxTotalList = glLowcarbonActionSignDetailDao.selectByUserId(UserUtils.getUserId());
        //获取今天日期
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plusDays(1);
        //获取今天到21天之前每一天的日期
        List<LocalDate> currentDaysList = new ArrayList<>();
        LocalDate endDate = tomorrow.minusDays(maxTotalList.size());
        while (!tomorrow.isBefore(endDate)) {
            tomorrow = tomorrow.minusDays(1);
            currentDaysList.add(tomorrow);
        }
        Long userId = UserUtils.getUserId();
        //补签
        int type = 1;
        //查询已经补签过多少次
        List<GlLowcarbonActionSignDetailDO> detailList = glLowcarbonActionSignDetailDao.selectIsSupple(userId,type);
        //查询补签配置
        GlAppletConfig isExit = glAppletConfigDao.selectByKey(CONFIG_TRIFLE_SETTINGS);
        ConfigDto configDto = JSON.parseObject(isExit.getParamValue(),ConfigDto.class);
        if(configDto.getMonthMax() > 0 && detailList.size() >= configDto.getMonthMax()){
            throw new BizException("补签次数已用完");
        }
        GlUser glUser = glUserDao.selectById(userId);
        if(Objects.nonNull(glUser)){
            //判断当前用户积分是否够补签
            if(glUser.getPointRemain() < configDto.getSupplePoints()){
                throw new BizException("您的积分不足,无法补签");
            }
        }
        GlLowcarbonActionExecSignDO glLowcarbonActionExecSignDO = new GlLowcarbonActionExecSignDO();
        glLowcarbonActionExecSignDO.setUserId(userId);
        glLowcarbonActionExecSignDO.setCreatedAt(LocalDateTime.now());
        glLowcarbonActionExecSignDO.setMobileSha256(UserUtils.getMobileSha256());
        glLowcarbonActionExecSignDO.setExecuteDate(suppleSignSaveDto.getDate());
        glLowcarbonActionExecSignDao.insert(glLowcarbonActionExecSignDO);
        //保存明细表
        GlLowcarbonActionSignDetailDO todayMessage = new GlLowcarbonActionSignDetailDO();
        todayMessage.setUserId(userId);
        todayMessage.setCreatedAt(LocalDateTime.now());
        todayMessage.setMobileSha256(UserUtils.getMobileSha256());
        todayMessage.setPoint(configDto.getSupplePoints() * -1);
        todayMessage.setSignDate(suppleSignSaveDto.getDate());
        todayMessage.setType(1);
        glLowcarbonActionSignDetailDao.insert(todayMessage);

        /**
         * 保存积分表(存扣减的积分)
         */
        glPointsService.deductionPoints(configDto.getSupplePoints() * -1,todayMessage.getId());

        /**
         * 补签完查询连续签到天数
         */
        GlLowcarbonActionSignDO glLowcarbonActionSignDO = glLowcarbonActionSignDao.getUserTrifle(userId);
        GlLowcarbonActionSignDO signDO = new GlLowcarbonActionSignDO();
        //设置比连续签到标志位
        int index = 0;
        if(Objects.nonNull(glLowcarbonActionSignDO)){
            //循环日期集合
            for (LocalDate item:currentDaysList) {
                //查询当前日期是否签到
                GlLowcarbonActionSignDetailDO isExitDat = glLowcarbonActionSignDetailDao.selectByUserAndLocal(item,userId);
                if(Objects.nonNull(isExitDat)){
                    index++;
                }else {
                    break;
                }
            }
            signDO.setRepeatSignTotal(index);
            signDO.setUserId(userId);
            signDO.setUpdatedAt(LocalDateTime.now());
            signDO.setMobileSha256(mobieShare);
            signDO.setId(glLowcarbonActionSignDO.getId());
            signDO.setLastSignDate(LocalDate.now());
            glLowcarbonActionSignDao.updateById(signDO);
        }else {
            signDO.setLastSignDate(LocalDate.now());
            signDO.setCreatedAt(LocalDateTime.now());
            signDO.setMobileSha256(UserUtils.getMobileSha256());
            signDO.setUserId(userId);
            signDO.setRepeatSignTotal(1);
            glLowcarbonActionSignDao.insert(signDO);
        }
        /**
         * 保存任务奖励周期表
         */
        GlLowcarbonActionRewardTaskDO glLowcarbonActionRewardTaskDO = glLowcarbonActionRewardTaskDao.selectByUserId(userId);
        GlLowcarbonActionRewardTaskDO saveDate = new GlLowcarbonActionRewardTaskDO();
        if(Objects.isNull(glLowcarbonActionRewardTaskDO)){
            saveDate.setCreatedAt(LocalDateTime.now());
            saveDate.setMobileSha256(mobieShare);
            saveDate.setLastSignDate(LocalDate.now());
            saveDate.setRepeatSignTotal(1);
            saveDate.setUserId(userId);
            saveDate.setSigned7RewardState(0);
            saveDate.setSigned14RewardState(0);
            saveDate.setSigned21RewardState(0);
            saveDate.setResetDate(LocalDate.now());
            saveDate.setStartDate(LocalDate.now());
            saveDate.setLastSignDate(LocalDate.now());
            glLowcarbonActionRewardTaskDao.insert(saveDate);
        }else {
            if(index >=7 && index < 14){
                GlLowcarbonActionRewardDO glLowcarbonActionRewardDO = glLowcarbonActionRewardDao.selectByUserIdAndType(mobieShare);
                if (Objects.isNull(glLowcarbonActionRewardDO)){
                    GlLowcarbonActionRewardTaskDO isExitTask = glLowcarbonActionRewardTaskDao.selectByUserId(userId);
                    if(Objects.nonNull(isExitTask) && isExitTask.getSigned7RewardState() == 0){
                        saveDate.setSigned7RewardState(1);
                        //保存奖励发放
                        GlLowcarbonActionRewardDO rewardDO = new GlLowcarbonActionRewardDO();
                        rewardDO.setMobileSha256(mobieShare);
                        rewardDO.setCreatedAt(LocalDateTime.now());
                        rewardDO.setPoint(configDto.getSevenPoints());
                        rewardDO.setRewardDate(LocalDate.now());
                        rewardDO.setState(0);
                        rewardDO.setType(1);
                        glLowcarbonActionRewardDao.insert(rewardDO);
                    }
                }
            }
            if(index >= 14 && index <21){
                //保存奖励发放
                GlLowcarbonActionRewardDO glLowcarbonActionRewardDO = glLowcarbonActionRewardDao.selectByUserIdAndfourteen(mobieShare);
                GlLowcarbonActionRewardTaskDO isExitTask = glLowcarbonActionRewardTaskDao.selectByUserId(userId);
                if(Objects.isNull(glLowcarbonActionRewardDO)){
                    if(Objects.nonNull(isExitTask) && isExitTask.getSigned14RewardState() == 0) {
                        saveDate.setSigned14RewardState(1);
                        GlLowcarbonActionRewardDO rewardDO = new GlLowcarbonActionRewardDO();
                        rewardDO.setMobileSha256(mobieShare);
                        rewardDO.setCreatedAt(LocalDateTime.now());
                        rewardDO.setPoint(configDto.getFourteenPoints());
                        rewardDO.setRewardDate(LocalDate.now());
                        rewardDO.setState(0);
                        rewardDO.setType(2);
                        glLowcarbonActionRewardDao.insert(rewardDO);
                    }
                }
            }
            if(index>=21){
                GlLowcarbonActionRewardTaskDO isExitTask = glLowcarbonActionRewardTaskDao.selectByUserId(userId);
                GlLowcarbonActionRewardDO glLowcarbonActionRewardDO = glLowcarbonActionRewardDao.selectByUserIdAndTwenty(mobieShare);
                if(Objects.isNull(glLowcarbonActionRewardDO)) {
                    if (Objects.nonNull(isExitTask) && isExitTask.getSigned21RewardState() == 0) {
                        saveDate.setSigned21RewardState(1);
                        //保存奖励发放
                        GlLowcarbonActionRewardDO rewardDO = new GlLowcarbonActionRewardDO();
                        rewardDO.setMobileSha256(mobieShare);
                        rewardDO.setCreatedAt(LocalDateTime.now());
                        rewardDO.setPoint(configDto.getTwentyOnePoints());
                        rewardDO.setRewardDate(LocalDate.now());
                        rewardDO.setState(0);
                        rewardDO.setType(3);
                        glLowcarbonActionRewardDao.insert(rewardDO);
                    }
                }
            }
            //保存奖励发放
            saveDate.setRepeatSignTotal(index);
            saveDate.setLastSignDate(LocalDate.now());
            saveDate.setUpdatedAt(LocalDateTime.now());
            saveDate.setId(glLowcarbonActionRewardTaskDO.getId());
            glLowcarbonActionRewardTaskDao.updateById(saveDate);
        }
        return configDto.getSupplePoints() * -1;

    }

    @Override
    public List<LitPictureListVo> litPicture() {
        //查询图片前缀
        String imagePrefix = glAppletConfigService.getImagePrefix();
        List<LitPictureListVo> returnList = new ArrayList<>();
        //查询所有小事
        List<GlLowcarbonActionDO> listTrifle = glLowcarbonActionCateDao.selectAll();
        Long userId = UserUtils.getUserId();

        for (GlLowcarbonActionDO item:listTrifle) {
            LitPictureListVo litPictureListVo = new LitPictureListVo();
           List<GlLowcarbonActionExecSignDO>  alreadyList = glLowcarbonActionExecSignDao.selectByUserAndTri(userId,item.getId());
            String lightUrl = imagePrefix+item.getIconLight();
            String darkUrl = imagePrefix+item.getIconDark();
            litPictureListVo.setIconLight(lightUrl);
            litPictureListVo.setIconDark(darkUrl);
            litPictureListVo.setCount(alreadyList.size());
            litPictureListVo.setId(item.getId());
            litPictureListVo.setTitle(item.getTitle());
            returnList.add(litPictureListVo);
        }

        return returnList;
    }

    @Override
    public List<SignListVo> getSignList(SignListDto signListDto) {
        Long userId = UserUtils.getUserId();
        List<SignListVo> list = new ArrayList<>();
        //判断是否是闰年
        boolean isLeap = DateUtil.isLeapYear(signListDto.getYear());
        //获取指定月份天数
        int days = Month.of(signListDto.getMonth()).length(isLeap);
        for (int i =1;i<= days;i++) {
            SignListVo signListVo = new SignListVo();
            String month = String.valueOf(signListDto.getMonth());
            String day = String.valueOf(i);
            if(signListDto.getMonth() < 10){
                month = "0"+month;
            }
            if(i < 10){
                day = "0"+day;
            }
            String date = signListDto.getYear()+"-"+month+"-"+day;
            //将string 转为localdate
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate format = LocalDate.parse(date, fmt);
            //判断当前年月日是否签到
            GlLowcarbonActionSignDetailDO item = glLowcarbonActionSignDetailDao.getDateIsExit(userId,format);
            if(Objects.nonNull(item)){
                signListVo.setDate(item.getSignDate());
                signListVo.setPoints(item.getPoint());
                signListVo.setStatus(0);
            }else {
                signListVo.setDate(format);
                signListVo.setPoints(0);
                signListVo.setStatus(1);
            }
            list.add(signListVo);
        }

        return list;
    }

    @Override
    public GetIsRewardVo getIsReward() {
        GetIsRewardVo getIsRewardVo = new GetIsRewardVo();
        Long userId = UserUtils.getUserId();
        GlLowcarbonActionRewardTaskDO glLowcarbonActionRewardTaskDO = glLowcarbonActionRewardTaskDao.selectByUserId(userId);
        if (Objects.nonNull(glLowcarbonActionRewardTaskDO)){
            getIsRewardVo.setSigned7RewardState(glLowcarbonActionRewardTaskDO.getSigned7RewardState());
            getIsRewardVo.setSigned14RewardState(glLowcarbonActionRewardTaskDO.getSigned14RewardState());
            getIsRewardVo.setSigned21RewardState(glLowcarbonActionRewardTaskDO.getSigned21RewardState());
        }else {
            getIsRewardVo.setSigned21RewardState(0);
            getIsRewardVo.setSigned7RewardState(0);
            getIsRewardVo.setSigned14RewardState(0);
        }
        return getIsRewardVo;
    }

    @Override
    public RewardPointsVo getRewardsPoints() {

        RewardPointsVo rewardPointsVo = new RewardPointsVo();
        GlAppletConfig isExit = glAppletConfigDao.selectByKey(CONFIG_TRIFLE_SETTINGS);
        ConfigDto configDto = JSON.parseObject(isExit.getParamValue(),ConfigDto.class);
        if(Objects.nonNull(configDto)){
            rewardPointsVo.setSevenDayPoints(configDto.getSevenPoints());
            rewardPointsVo.setFourteenDayPoints(configDto.getFourteenPoints());
            rewardPointsVo.setTwentyOneDayPoints(configDto.getTwentyOnePoints());
        }
        return rewardPointsVo;
    }

    @Override
    public void getMyPoints(SupplePointsDto supplePointsDto) {
        String mobile = UserUtils.getMobileSha256();
        if(supplePointsDto.getDays() == 7){
            GlLowcarbonActionRewardDO glLowcarbonActionRewardDO = glLowcarbonActionRewardDao.selectByUserIdAndType(mobile);
            GlLowcarbonActionRewardDO update = new GlLowcarbonActionRewardDO();
            update.setId(glLowcarbonActionRewardDO.getId());
            update.setState(1);
            update.setReceiveDate(LocalDateTime.now());
            glLowcarbonActionRewardDao.updateById(update);
            //修改task表
            GlLowcarbonActionRewardTaskDO glLowcarbonActionRewardTaskDO = glLowcarbonActionRewardTaskDao.selectByUserId(UserUtils.getUserId());
            GlLowcarbonActionRewardTaskDO taskDO = new GlLowcarbonActionRewardTaskDO();
            taskDO.setId(glLowcarbonActionRewardTaskDO.getId());
            taskDO.setSigned7RewardState(2);
            glLowcarbonActionRewardTaskDao.updateById(taskDO);

            GlAppletConfig isExit = glAppletConfigDao.selectByKey(CONFIG_TRIFLE_SETTINGS);
            ConfigDto configDto = JSON.parseObject(isExit.getParamValue(),ConfigDto.class);
            //发放积分表
            if(Objects.nonNull(configDto)){
                glPointsService.getDaysPoints(configDto.getSevenPoints(),glLowcarbonActionRewardDO.getId());
            }
        }
        if(supplePointsDto.getDays() == 14){
            GlLowcarbonActionRewardDO glLowcarbonActionRewardDO = glLowcarbonActionRewardDao.selectByUserIdAndfourteen(mobile);
            GlLowcarbonActionRewardDO update = new GlLowcarbonActionRewardDO();
            update.setId(glLowcarbonActionRewardDO.getId());
            update.setState(1);
            update.setReceiveDate(LocalDateTime.now());
            glLowcarbonActionRewardDao.updateById(update);
            //修改task表
            GlLowcarbonActionRewardTaskDO glLowcarbonActionRewardTaskDO = glLowcarbonActionRewardTaskDao.selectByUserId(UserUtils.getUserId());
            GlLowcarbonActionRewardTaskDO taskDO = new GlLowcarbonActionRewardTaskDO();
            taskDO.setId(glLowcarbonActionRewardTaskDO.getId());
            taskDO.setSigned14RewardState(2);
            glLowcarbonActionRewardTaskDao.updateById(taskDO);

            GlAppletConfig isExit = glAppletConfigDao.selectByKey(CONFIG_TRIFLE_SETTINGS);
            ConfigDto configDto = JSON.parseObject(isExit.getParamValue(),ConfigDto.class);
            //发放积分表
            if(Objects.nonNull(configDto)){
                glPointsService.getDaysPoints(configDto.getFourteenPoints(),glLowcarbonActionRewardDO.getId());
            }
        }
        if(supplePointsDto.getDays() == 21){
            GlLowcarbonActionRewardDO glLowcarbonActionRewardDO = glLowcarbonActionRewardDao.selectByUserIdAndTwenty(mobile);
            GlLowcarbonActionRewardDO update = new GlLowcarbonActionRewardDO();
            update.setId(glLowcarbonActionRewardDO.getId());
            update.setState(1);
            update.setReceiveDate(LocalDateTime.now());
            glLowcarbonActionRewardDao.updateById(update);

            //修改task表
            GlLowcarbonActionRewardTaskDO glLowcarbonActionRewardTaskDO = glLowcarbonActionRewardTaskDao.selectByUserId(UserUtils.getUserId());
            GlLowcarbonActionRewardTaskDO taskDO = new GlLowcarbonActionRewardTaskDO();
            taskDO.setId(glLowcarbonActionRewardTaskDO.getId());
            taskDO.setSigned21RewardState(2);
            glLowcarbonActionRewardTaskDao.updateById(taskDO);

            GlAppletConfig isExit = glAppletConfigDao.selectByKey(CONFIG_TRIFLE_SETTINGS);
            ConfigDto configDto = JSON.parseObject(isExit.getParamValue(),ConfigDto.class);
            //发放积分表
            if(Objects.nonNull(configDto)){
                glPointsService.getDaysPoints(configDto.getTwentyOnePoints(),glLowcarbonActionRewardDO.getId());
            }
        }

    }

    @Override
    public PointsAndDaysVo getAllSuppleDaysAndPoints() {
        PointsAndDaysVo pointsAndDaysVo = new PointsAndDaysVo();
        Long userId = UserUtils.getUserId();
        GlUser glUser = glUserDao.selectById(userId);
        if(Objects.nonNull(glUser)){
            pointsAndDaysVo.setPoints(Objects.nonNull(glUser.getPointRemain())?glUser.getPointRemain():0L);
        }
        GlLowcarbonActionRewardTaskDO glLowcarbonActionRewardTaskDO = glLowcarbonActionRewardTaskDao.selectByUserId(userId);
        if(Objects.nonNull(glLowcarbonActionRewardTaskDO)){
            pointsAndDaysVo.setDays(glLowcarbonActionRewardTaskDO.getRepeatSignTotal());
        }
        return pointsAndDaysVo;
    }

    @Override
    public MySignVo myLight() {

        String imagePrefix = glAppletConfigService.getImagePrefix();
        MySignVo mySignVo = new MySignVo();
        mySignVo.setCount(0);
        mySignVo.setIconLights(Lists.newArrayList());
        List<String> iconLights = glLowcarbonActionExecSignDao.getMyLight(UserUtils.getMobileSha256());
        if(CollUtil.isNotEmpty(iconLights)){
            List<String> list = iconLights.stream().map(iconLight -> imagePrefix + iconLight).collect(Collectors.toList());
            mySignVo.setCount(iconLights.size());
            mySignVo.setIconLights(CollUtil.sub(list,0,3));
        }
        return mySignVo;
    }
}
