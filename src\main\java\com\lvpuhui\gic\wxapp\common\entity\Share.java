package com.lvpuhui.gic.wxapp.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

/**
 * 分享表
 */
@Schema(description = "分享表")
@Getter
@Setter
@TableName(value = "gl_share")
public class Share {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键")
    @NotNull(message = "主键不能为null")
    private Long id;

    /**
     * 分享类型 0：活动进度 1:勋章分享
     */
    @TableField(value = "`type`")
    @Schema(description = "分享类型 0：活动进度 1:勋章分享")
    private Integer type;

    /**
     * 分享来源ID,ID为0则是获得进度的
     */
    @TableField(value = "source_id")
    @Schema(description = "分享来源ID,ID为0则是获得进度的")
    private Long sourceId;

    /**
     * 手机号sha256
     */
    @TableField(value = "mobile_sha256")
    @Schema(description = "手机号sha256")
    @Size(max = 100, message = "手机号sha256最大长度要小于 100")
    private String mobileSha256;

    /**
     * 积分
     */
    @TableField(value = "points")
    @Schema(description = "积分")
    private Integer points;

    /**
     * 版本号记录
     */
    @TableField(value = "version")
    @Schema(description = "版本号记录")
    @Size(max = 8, message = "版本号记录最大长度要小于 8")
    private String version;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description = "创建时间")
    private Date created;
}