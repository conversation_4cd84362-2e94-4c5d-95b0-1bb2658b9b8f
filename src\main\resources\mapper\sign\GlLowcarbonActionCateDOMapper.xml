<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.sign.dao.GlLowcarbonActionCateDao">

    <select id="signClassIfyTrifle" resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionCateDO">
        select id,title,sort,is_show as isShow,updated_at as updatedAt,created_at as createdAt,deleted_at as deletedAt from gl_lowcarbon_action_cate where is_show = 0
    </select>
    <select id="selectAll" resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionDO">
        select id,title,cate_id as cateId,description,icon_light as iconLight,icon_dark as iconDark,sort,is_show as isShow,updated_at as updatedAt,created_at as createdAt,deleted_at as deletedAt from gl_lowcarbon_action where is_show = 0
    </select>
</mapper>
