<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.medal.dao.GlMedalUserDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.medal.entity.GlMedalUser">
    <!--@mbg.generated-->
    <!--@Table gl_medal_user-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="medal_id" jdbcType="BIGINT" property="medalId" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="mobile_sha256" jdbcType="VARCHAR" property="mobileSha256" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, medal_id, `state`, mobile_sha256, created
  </sql>

  <select id="getObtainMedalIds" resultType="java.lang.Long">
    select medal_id from gl_medal_user where mobile_sha256 = #{mobileSha256}
  </select>
</mapper>