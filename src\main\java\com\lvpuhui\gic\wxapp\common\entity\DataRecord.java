package com.lvpuhui.gic.wxapp.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据记录表
 */
@Schema(description="数据记录表")
@Getter
@Setter
@TableName(value = "gl_data_record")
public class DataRecord {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键")
    @NotNull(message = "主键不能为null")
    private Long id;

    /**
     * 手机号sha256
     */
    @TableField(value = "mobile_sha256")
    @Schema(description="手机号sha256")
    @Size(max = 100,message = "手机号sha256最大长度要小于 100")
    private String mobileSha256;

    /**
     * 数据json
     */
    @TableField(value = "data_json")
    @Schema(description="数据json")
    private String dataJson;

    /**
     * 类型
     */
    @TableField(value = "`type`")
    @Schema(description="类型")
    private Integer type;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description="创建时间")
    private Date created;

    /**
     * 日期版本号
     */
    @TableField(value = "version")
    @Schema(description="日期版本号")
    @Size(max = 8,message = "日期版本号最大长度要小于 8")
    private String version;
}