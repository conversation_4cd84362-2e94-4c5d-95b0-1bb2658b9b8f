<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.sign.dao.GlLowcarbonActionRewardTaskDao">

    <select id="selectByUserId" resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionRewardTaskDO">
        select id,
        user_id as userId,
        mobile_sha256 as mobileSha256,
        repeat_sign_total as repeatSignTotal,
        signed7_reward_state as signed7RewardState,
        signed14_reward_state as signed14RewardState,
        signed21_reward_state as signed21RewardState,
        start_date as startDate,
        end_date as endDate,
        reset_date as resetDate,
        last_sign_date as lastSignDate,
        updated_at as updatedAt,
        created_at as createdAt from gl_lowcarbon_action_reward_task where user_id = #{userId}
    </select>
</mapper>
