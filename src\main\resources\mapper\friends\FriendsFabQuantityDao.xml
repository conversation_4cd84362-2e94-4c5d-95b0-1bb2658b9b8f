<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.friends.dao.FriendsFabQuantityDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.friends.entity.FriendsFabQuantity">
    <!--@mbg.generated-->
    <!--@Table gl_friends_fab_quantity-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mobile_sha256" jdbcType="VARCHAR" property="mobileSha256" />
    <result column="fab_number" jdbcType="BIGINT" property="fabNumber" />
    <result column="version" jdbcType="VARCHAR" property="version" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, mobile_sha256, fab_number, version
  </sql>

  <insert id="insertUpdate">
    insert into gl_friends_fab_quantity (mobile_sha256, fab_number, version)
    values (#{mobileSha256}, 1, #{version})
    on duplicate key update fab_number = fab_number + 1
  </insert>
</mapper>