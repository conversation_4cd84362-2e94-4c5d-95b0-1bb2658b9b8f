<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.common.dao.DataRecordDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.common.entity.DataRecord">
    <!--@mbg.generated-->
    <!--@Table gl_data_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mobile_sha256" jdbcType="VARCHAR" property="mobileSha256" />
    <result column="data_json" jdbcType="VARCHAR" property="dataJson" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="version" jdbcType="VARCHAR" property="version" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, mobile_sha256, data_json, `type`, created, version
  </sql>

  <select id="getCountByMobileSha256" resultType="int">
    select count(1) from gl_data_record where mobile_sha256 = #{mobileSha256}
    and type = #{type} and version = #{version}
  </select>
</mapper>