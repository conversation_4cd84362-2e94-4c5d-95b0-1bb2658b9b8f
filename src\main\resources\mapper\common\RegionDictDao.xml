<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.common.dao.RegionDictDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.common.entity.RegionDict">
    <!--@mbg.generated-->
    <!--@Table gl_region_dict-->
    <id column="region" jdbcType="VARCHAR" property="region" />
    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    region, region_name
  </sql>
</mapper>