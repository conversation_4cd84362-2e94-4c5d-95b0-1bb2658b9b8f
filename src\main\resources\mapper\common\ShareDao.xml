<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.common.dao.ShareDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.common.entity.Share">
    <!--@mbg.generated-->
    <!--@Table gl_share-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="mobile_sha256" jdbcType="VARCHAR" property="mobileSha256" />
    <result column="points" jdbcType="INTEGER" property="points" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `type`, source_id, mobile_sha256, points, version, created
  </sql>

  <select id="getVersionCountByMobileSha256" resultType="java.lang.Integer">
    select count(1) from gl_share where mobile_sha256 = #{mobileSha256} and version = #{version}
  </select>

  <select id="getCountByMobileSha256" resultType="int">
    select count(1) from gl_share where mobile_sha256 = #{mobileSha256}
  </select>
</mapper>