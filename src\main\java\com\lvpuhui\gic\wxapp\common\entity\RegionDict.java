package com.lvpuhui.gic.wxapp.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 区域字典表
 */
@Schema(description="区域字典表")
@Getter
@Setter
@TableName(value = "gl_region_dict")
public class RegionDict {
    /**
     * 区域code
     */
    @TableId(value = "region", type = IdType.INPUT)
    @Schema(description="区域code")
    @Size(max = 10,message = "区域code最大长度要小于 10")
    @NotBlank(message = "区域code不能为空")
    private String region;

    /**
     * 区域名称
     */
    @TableField(value = "region_name")
    @Schema(description="区域名称")
    @Size(max = 100,message = "区域名称最大长度要小于 100")
    private String regionName;
}