package com.lvpuhui.gic.wxapp.dao;

import com.lvpuhui.gic.wxapp.entity.GlTickoff;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 打卡数据 (用户扫码) (GlTickoff)表数据库访问层
 * <AUTHOR>
 * @since 2022年5月5日 19:21:27
 */
public interface GlTickoffDao extends BaseMapper<GlTickoff> {

    @Select("select count(1) from gl_tickoff where mobile_sha256 = #{mobileSha256}")
    int getCountByMobileSha256(@Param("mobileSha256") String mobileSha256);
}
