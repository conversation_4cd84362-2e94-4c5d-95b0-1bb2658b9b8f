package com.lvpuhui.gic.wxapp.clock.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Schema(description = "用户扩展表")
@Getter
@Setter
@TableName(value = "gl_user_extend",autoResultMap = true)
public class UserExtend {
    /**
     * 用户sha256
     */
    @TableId(value = "mobile_sha256", type = IdType.INPUT)
    @Schema(description="用户sha256")
    @Size(max = 100,message = "用户sha256最大长度要小于 100")
    @NotBlank(message = "用户sha256不能为空")
    private String mobileSha256;

    /**
     * 扩展信息json
     */
    @TableField(typeHandler = FastjsonTypeHandler.class, value = "extend_json")
    @Schema(description="扩展信息json")
    private UserExtendJson extendJson;
}