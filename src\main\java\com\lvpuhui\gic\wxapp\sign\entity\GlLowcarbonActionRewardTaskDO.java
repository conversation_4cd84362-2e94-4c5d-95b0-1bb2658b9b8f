package com.lvpuhui.gic.wxapp.sign.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 连续签到奖励任务周期表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_lowcarbon_action_reward_task")
public class GlLowcarbonActionRewardTaskDO extends Model<GlLowcarbonActionRewardTaskDO> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户手机号hash
     */
    private String mobileSha256;

    /**
     * 连续签到的天数
     */
    private Integer repeatSignTotal;

    /**
     * 连续签到7天领取状态（0不可领取;1待领取;2已领取;）
     */
    private Integer signed7RewardState;

    /**
     * 连续签到14天领取状态
     */
    private Integer signed14RewardState;

    /**
     * 连续签到21天领取状态
     */
    private Integer signed21RewardState;

    /**
     * 连续奖励任务开始日期
     */
    private LocalDate startDate;

    /**
     * 连续任务奖励结束日期
     */
    private LocalDate endDate;

    /**
     * 达到21任务重置日期
     */
    private LocalDate resetDate;

    /**
     * 最后一次签到日期
     */
    private LocalDate lastSignDate;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
