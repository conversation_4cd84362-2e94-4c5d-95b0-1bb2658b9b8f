<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.friends.dao.FriendsRankRecordDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.friends.entity.FriendsRankRecord">
    <!--@mbg.generated-->
    <!--@Table gl_friends_rank_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="friends_mobile_sha256" jdbcType="VARCHAR" property="friendsMobileSha256" />
    <result column="mobile_sha256" jdbcType="VARCHAR" property="mobileSha256" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, version, friends_mobile_sha256, mobile_sha256, created
  </sql>
</mapper>