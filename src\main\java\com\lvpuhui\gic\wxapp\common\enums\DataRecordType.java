package com.lvpuhui.gic.wxapp.common.enums;

import lombok.Getter;

@Getter
public enum DataRecordType {
    CLOCK_IN(0,"打卡"),
    GUARD(1,"守护"),
    ENTER_SECOND_STAGE(2,"进入第二阶段"),
    PASS_FIRST_STAGE(3,"通过第一阶段-暂时不用"),
    ;

    private final Integer type;

    private final String describe;

    DataRecordType(Integer type, String describe) {
        this.type = type;
        this.describe = describe;
    }
}
