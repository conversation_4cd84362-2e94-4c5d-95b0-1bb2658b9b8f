# 第二阶段判断逻辑重构总结

## 概述
根据新的业务需求，重构了第二阶段判断逻辑，从基于减排量判断改为基于守护记录判断，简化了业务逻辑。

## 修改内容

### 1. 枚举类修改
#### DataRecordType.java
```java
FUND_PROGRESS(4,"基金发放进度"), // 新增
```

### 2. 判断逻辑重构

#### 修改前的逻辑（复杂）
```java
// 查询是否已进入第二阶段 (type = 2)
// 如果没有记录：
//   1. 查询用户总减排量
//   2. 判断减排量是否 >= 80KG
//   3. 或者判断活动是否结束
//   4. 满足条件则记录进入第二阶段
```

#### 修改后的逻辑（简化）
```java
// 1. 查询守护记录 (type = 1) - 判断是否能进入
// 2. 判断：有守护记录 或 活动结束
// 3. 如果满足条件：
//    - 查询是否已记录进入第二阶段 (type = 2)
//    - 如果未记录：记录进入第二阶段（含总减排量）
// 4. 如果已进入第二阶段：发放基金
```

### 3. 核心方法修改

#### processEmissionBasedFund方法
- **移除**：减排量阈值相关的计算和判断
- **新增**：`checkCanEnterSecondStage()` 方法调用
- **简化**：业务常量定义，只保留基金费率和活动结束时间

#### processEmissionBasedFundForBatch方法
- **同样的修改**：使用新的判断逻辑
- **简化**：移除减排量相关的复杂计算

### 4. 新增方法

#### checkCanEnterSecondStage方法
```java
private boolean checkCanEnterSecondStage(String mobileSha256, Date activityEndTime)
```
- **功能**：检查用户是否满足进入第二阶段的条件
- **逻辑**：查询守护记录 或 判断活动结束

#### recordEnterSecondStageWithTotalEmission方法
```java
private void recordEnterSecondStageWithTotalEmission(String mobileSha256)
```
- **功能**：记录用户进入第二阶段，包含总减排量
- **数据**：查询用户当前总减排量并存储到dataJson

### 5. 删除的方法
- `handleEnterSecondStageCheck` - 旧的第二阶段检查逻辑
- `handleEnterSecondStageCheckForBatch` - 旧的批量处理检查逻辑
- `recordEnterSecondStage` - 旧的记录方法

## 业务逻辑对比

### 进入第二阶段的条件

#### 修改前
- 用户总减排量 >= 80KG
- 或者活动结束时间已过

#### 修改后
- 用户有守护记录（调用过/lowCarbonClockIn/guard接口）
- 或者活动结束时间已过

### 判断流程

#### 修改前（复杂）
1. 查询是否已进入第二阶段
2. 如果没有：查询总减排量 → 计算千克 → 比较阈值 → 判断活动时间
3. 满足条件：记录进入第二阶段

#### 修改后（简化）
1. 查询守护记录 → 判断活动时间
2. 满足条件：查询是否已记录进入第二阶段
3. 如果未记录：记录进入第二阶段

## 数据存储

### gl_data_record表
- **type = 1**：守护记录（已存在，用于判断）
- **type = 2**：进入第二阶段记录（新增时包含总减排量）
- **type = 4**：基金发放进度记录

### 数据格式
```json
// type = 2 的 dataJson
{"totalEmissionGrams": "150000"}

// type = 4 的 dataJson  
{"lastDetailId": "12345"}
```

## 性能优化

### 1. 减少计算
- 不再需要查询和计算总减排量来判断80KG阈值
- 简化为简单的记录查询

### 2. 逻辑简化
- 判断条件更加直观：守护记录 或 活动结束
- 减少了复杂的数值计算和比较

### 3. 代码简洁
- 删除了不必要的方法和参数
- 业务逻辑更加清晰

## 关键改进

1. **判断条件简化**：从复杂的减排量计算改为简单的记录查询
2. **性能提升**：减少了数据库查询和数值计算
3. **逻辑清晰**：守护行为 → 进入第二阶段 → 发放基金
4. **代码维护性**：删除冗余代码，保留核心逻辑

## 业务含义

### 守护记录的作用
- 用户调用 `/lowCarbonClockIn/guard` 接口进行守护
- 系统记录守护行为到 `gl_data_record` 表（type=1）
- 有守护记录表示用户有资格进入第二阶段

### 第二阶段的意义
- 只有进入第二阶段的用户才能获得基金
- 进入条件：守护行为 或 活动结束
- 进入时记录用户当时的总减排量

## 编译状态

✅ 所有代码编译通过，无错误和警告
✅ 成功简化了业务逻辑
✅ 提升了代码性能和可维护性

## 总结

这次重构成功简化了第二阶段的判断逻辑：
- **从复杂的减排量计算** → **简单的记录查询**
- **从数值比较判断** → **业务行为判断**
- **提升了性能** → **增强了可读性**

新的逻辑更加符合业务实际：用户通过守护行为或活动结束获得进入第二阶段的资格，然后基于减排量获得基金奖励。
