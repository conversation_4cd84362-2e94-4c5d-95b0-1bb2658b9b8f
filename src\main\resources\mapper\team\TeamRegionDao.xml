<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.team.dao.TeamRegionDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.team.entity.TeamRegion">
    <!--@mbg.generated-->
    <!--@Table gl_team_region-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, region_name, created
  </sql>
</mapper>