package com.lvpuhui.gic.wxapp.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.common.entity.DataRecord;
import org.apache.ibatis.annotations.Param;

public interface DataRecordDao extends BaseMapper<DataRecord> {

    int getCountByMobileSha256(@Param("mobileSha256") String mobileSha256, @Param("type") Integer type, @Param("version") String version);
}