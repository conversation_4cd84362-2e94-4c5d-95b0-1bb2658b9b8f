package com.lvpuhui.gic.wxapp.infrastructure.utils;

import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

@Component
public class MessageUtils {
	
    private static MessageSource messageSource;

    public MessageUtils(MessageSource messageSource) {
    	MessageUtils.messageSource = messageSource;
    }

    public static String get(String msgKey, Object...args) {
        try {
            return messageSource.getMessage(msgKey, args, LocaleContextHolder.getLocale());
        } catch (Exception e) {
        	e.printStackTrace();
            return msgKey;
        }
    }
}
