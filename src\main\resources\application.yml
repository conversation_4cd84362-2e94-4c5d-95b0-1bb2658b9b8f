server:
  port: 9083
spring:
  jackson:
    time-zone: GMT+8
    dateFormat: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true
  profiles:
    active: @profile.active@ #此处由maven的环境选择决定
  # 开启循环依赖
  main:
    allow-circular-references: true
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  mapper-locations: classpath:/mapper/*/*Dao.xml,classpath:/mapper/*Dao.xml,classpath:/mapper/*/*Mapper.xml,classpath:/mapper/*Mapper.xml
  type-aliases-package: com.lvpuhui.gic.wxapp.entity,com.lvpuhui.gic.wxapp.*.entity
  global-config:
    banner: false
logging:
  config: classpath:logback-springcloud.xml
  level:
    com.baomidou: info