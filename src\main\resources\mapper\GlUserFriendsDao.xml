<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.dao.GlUserFriendsDao">

  <select id="getMyFriendsList" resultType="com.lvpuhui.gic.wxapp.friends.dto.response.FriendsUserVo">
    SELECT
      gu.id as userId,
      gu.mobile_sha256,
      gu.avatar_url,
      gu.nick_name
    FROM
      gl_user_friends guf
        INNER JOIN gl_user gu ON guf.friends_sha256 = gu.mobile_sha256
    WHERE
      guf.mobile_sha256 = #{mobileSha256}
    ORDER BY
      guf.id
  </select>

  <select id="getCountByMobileSha256" resultType="int">
    select count(1) from gl_user_friends where mobile_sha256 = #{mobileSha256}
  </select>

  <select id="friendsNumberRank" resultType="com.lvpuhui.gic.wxapp.entity.vo.FriendsNumberRankVo">
    SELECT mobile_sha256, COUNT( id ) AS friendsNumber FROM gl_user_friends GROUP BY mobile_sha256
    ORDER BY friendsNumber DESC
    LIMIT 10
  </select>

  <select id="getInfluenceRankFirstUser" resultType="com.lvpuhui.gic.wxapp.entity.vo.RankFirstVo$RankFirstUserVo">
    SELECT
      gu.avatar_url,
      gu.nick_name
    FROM
      gl_user gu
        INNER JOIN ( SELECT mobile_sha256, COUNT( id ) AS friendsNumber FROM gl_user_friends GROUP BY mobile_sha256 ORDER BY friendsNumber DESC LIMIT 1 ) friendsRank ON gu.mobile_sha256 = friendsRank.mobile_sha256
  </select>
</mapper>