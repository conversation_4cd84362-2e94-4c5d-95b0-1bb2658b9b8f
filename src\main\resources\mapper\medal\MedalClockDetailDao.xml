<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.medal.dao.MedalClockDetailDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.medal.entity.MedalClockDetail">
    <!--@mbg.generated-->
    <!--@Table gl_medal_clock_detail-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="medal_id" jdbcType="BIGINT" property="medalId" />
    <result column="clock_in_id" jdbcType="BIGINT" property="clockInId" />
    <result column="clock_in_number" jdbcType="INTEGER" property="clockInNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, medal_id, clock_in_id, clock_in_number
  </sql>
</mapper>