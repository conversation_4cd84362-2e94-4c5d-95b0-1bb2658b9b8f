package com.lvpuhui.gic.wxapp.clock.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 配捐表
 */
@Schema(description="配捐表")
@Getter
@Setter
@TableName(value = "gl_donation")
public class Donation {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键")
    @NotNull(message = "主键不能为null")
    private Long id;

    /**
     * 手机号sha256
     */
    @TableField(value = "mobile_sha256")
    @Schema(description="手机号sha256")
    @Size(max = 100,message = "手机号sha256最大长度要小于 100")
    @NotBlank(message = "手机号sha256不能为空")
    private String mobileSha256;

    /**
     * 配捐类型 0：减排量配捐 1：助力值(积分)配捐
     */
    @TableField(value = "`type`")
    @Schema(description="配捐类型 0：减排量配捐 1：助力值(积分)配捐")
    private Integer type;

    /**
     * 配捐金额
     */
    @TableField(value = "amount")
    @Schema(description="配捐金额")
    private BigDecimal amount;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description="创建时间")
    private Date created;

    /**
     * 备注：使用场景看情况
     */
    @TableField(value = "notes")
    @Schema(description="备注：使用场景看情况")
    private String notes;
}