package com.lvpuhui.gic.wxapp.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.common.entity.Share;
import org.apache.ibatis.annotations.Param;

public interface ShareDao extends BaseMapper<Share> {

    Integer getVersionCountByMobileSha256(@Param("mobileSha256") String mobileSha256, @Param("version") String version);

    int getCountByMobileSha256(@Param("mobileSha256") String mobileSha256);
}