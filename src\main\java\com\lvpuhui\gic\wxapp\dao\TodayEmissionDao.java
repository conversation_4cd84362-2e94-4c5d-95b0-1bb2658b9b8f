package com.lvpuhui.gic.wxapp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.entity.TodayEmission;
import com.lvpuhui.gic.wxapp.entity.vo.RankFirstVo;
import com.lvpuhui.gic.wxapp.entity.vo.TodayRankVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TodayEmissionDao extends BaseMapper<TodayEmission> {

    int insertOnUpdate(@Param("todayEmission") TodayEmission todayEmission);

    List<TodayRankVo> getTodayRank(@Param("version") String version);

    RankFirstVo.RankFirstUserVo getTodayRankFirstUser(@Param("version") String version);
}