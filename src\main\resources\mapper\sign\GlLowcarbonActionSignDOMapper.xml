<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.sign.dao.GlLowcarbonActionSignDao">

    <select id="getUserTrifle" resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionSignDO">
        select
        id,
        user_id as userId,
        mobile_sha256 as mobileSha256,
        sign_total as signTotal,
        repeat_sign_total as repeatSignTotal,
        last_sign_date as lastSignDate,
        updated_at as updatedAt,
        created_at as createdAt
        from gl_lowcarbon_action_sign where user_id = #{userId}
    </select>
</mapper>
