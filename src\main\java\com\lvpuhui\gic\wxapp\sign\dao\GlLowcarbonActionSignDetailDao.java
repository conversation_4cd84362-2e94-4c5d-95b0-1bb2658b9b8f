package com.lvpuhui.gic.wxapp.sign.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionSignDetailDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 低碳小事签到明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
public interface GlLowcarbonActionSignDetailDao extends BaseMapper<GlLowcarbonActionSignDetailDO> {

    GlLowcarbonActionSignDetailDO getIsExit(@Param("userId") Long userId, @Param("today")LocalDate today);

    List<GlLowcarbonActionSignDetailDO> selectIsSupple(@Param("userId")Long userId, @Param("type")int type);

    GlLowcarbonActionSignDetailDO getDateIsExit(@Param("userId")Long userId, @Param("format")LocalDate format);

    GlLowcarbonActionSignDetailDO selectByUserAndLocal(@Param("today")LocalDate today, @Param("userId")Long userId);

    List<GlLowcarbonActionSignDetailDO> selectByUserId(@Param("userId") Long userId);
}
