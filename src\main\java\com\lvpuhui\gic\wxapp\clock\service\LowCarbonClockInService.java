package com.lvpuhui.gic.wxapp.clock.service;

import com.lvpuhui.gic.wxapp.clock.dto.PointsObtainDto;
import com.lvpuhui.gic.wxapp.clock.dto.request.ClockInDto;
import com.lvpuhui.gic.wxapp.clock.dto.response.*;
import com.lvpuhui.gic.wxapp.entity.vo.CertificateVo;

import java.math.BigDecimal;
import java.util.List;

public interface LowCarbonClockInService {

    void resetMedals();

    /**
     * 查询低碳打卡列表
     */
    List<LowCarbonClockInListVo> list();

    /**
     * 低碳打卡明细接口
     */
    List<LowCarbonClockInDetailListVo> detail(Integer page);

    /**
     * 查询用户相关信息
     */
    UserInfoVo userInfo(String mobileSha256);

    /**
     * 低碳打卡接口
     */
    BigDecimal clockIn(ClockInDto clockInDto);

    void saveDataRecord(String mobileSha256, ClockInDto clockInDto, String version);

    BigDecimal handlerDonation(String mobileSha256, int type, BigDecimal value);

    void medalHandler(String mobileSha256,Long userId);

    /**
     * 活动进度
     */
    ActivityProgressVo activityProgress(String mobileSha256);

    /**
     * 分享进度
     */
    void shareProgress();

    /**
     * 拍照打卡判断是否可打卡
     */
    boolean clockInJudge(Long clockInId);

    /**
     * 判断是否是新勋章
     */
    List<IsNewMedalVo> judgeNewMedal();

    /**
     * 处理用户打卡符合发放的勋章
     */
    void processUserClockInMedal(String secret, String mobileSha256);

    /**
     * 获取打卡详情
     */
    LowCarbonClockInDetailVo clockInDetail(Long id);

    /**
     * 获取证书
     */
    CertificateVo certificate();

    /**
     * 守护
     */
    void guard(Long id);

    /**
     * 拍照打卡碳账本接口
     */
    CarbonBookVo carbonBook();

    /**
     * 拍照打卡碳账本详情接口
     */
    CarbonBookDetailCountVo carbonBookDetail(Long id, Integer page);

    /**
     * 处理执行者检查逻辑-全部手机号
     * 检查用户是否满足进入执行者阶段，并处理基金发放逻辑
     * @param secret 密钥
     */
    void executorCheck(String secret);

    /**
     * 处理基于减排量的基金发放
     * 检查用户是否进入第二阶段，并根据减排量发放基金
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param emission 减排量（克）
     * @param detailId 打卡详情ID（用于数据溯源）
     */
    void processEmissionBasedFund(String mobileSha256, BigDecimal emission, Long detailId);

    /**
     * 批量处理基于减排量的基金发放
     * 根据打卡详情记录批量发放基金
     *
     * @param mobileSha256 用户手机号SHA256值
     */
    void processBatchEmissionFund(String mobileSha256,PointsObtainDto pointsObtainDto);
}