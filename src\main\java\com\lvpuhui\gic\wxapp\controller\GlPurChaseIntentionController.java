package com.lvpuhui.gic.wxapp.controller;

import com.baomidou.mybatisplus.extension.api.ApiController;
import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.entity.dto.PurchaseIntetionDto;
import com.lvpuhui.gic.wxapp.service.GlPurchaseIntentionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 购买意愿
 * <AUTHOR>
 */
@RestController
@RequestMapping("")
public class GlPurChaseIntentionController extends ApiController {

    private final static Logger logger = LoggerFactory.getLogger(GlPurChaseIntentionController.class);


    @Autowired
    private GlPurchaseIntentionService glPurchaseIntentionService;

    /**
     * 保存客户购买意愿
     * @param purchaseIntetionDto
     * @return
     */
    @PostMapping("/save_purchase")
    public R savePurchase(@RequestBody PurchaseIntetionDto purchaseIntetionDto){
        logger.info("当前purchaseIntetionDto对象的信息为{}",purchaseIntetionDto);
        glPurchaseIntentionService.savePurchase(purchaseIntetionDto);
        return R.ok("保存成功");
    }

}
