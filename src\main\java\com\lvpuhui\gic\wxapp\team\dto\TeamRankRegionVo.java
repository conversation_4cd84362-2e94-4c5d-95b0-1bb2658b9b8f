package com.lvpuhui.gic.wxapp.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "本地战队排名")
public class TeamRankRegionVo {

    /**
     * 是否加入战队 true:已经加入 false:未加入
     */
    private Boolean joinTeam;

    /**
     * 未加入战队所需信息
     */
    private TeamNotJoinVo teamNotJoinVo;

    /**
     * 战队集合-前20
     */
    private List<TeamRankRegionListVo> teamRankListVos;

    /**
     * 我的战队信息
     */
    private TeamRankRegionListVo currentTeamRank;
}
