package com.lvpuhui.gic.wxapp.clock.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Schema(description = "积分获取配置")
public class PointsObtainDto {

    @Schema(description = "分享勋章获得积分")
    private Integer shareMedalPoints = 0;

    @Schema(description = "分享活动进度获得积分")
    private Integer shareActivityPoints = 0;

    @Schema(description = "好友点赞获得积分")
    private Integer friendsLikesPoints = 0;

    @Schema(description = "每1分钱配捐对应减排量克数")
    private BigDecimal donationEmission;

    @Schema(description = "减排量配捐钱数上限")
    private BigDecimal donationEmissionMax;

    @Schema(description = "每1分钱配捐对应碳积分")
    private Integer donationPoints;

    @Schema(description = "助力值配捐钱数上限")
    private BigDecimal donationPointsMax;

    @Schema(description = "设置碳中和血条上限值（单位KG）")
    private BigDecimal carbonEmissionLimit;

    @Schema(description = "请设置活动结束时间")
    private String activityEndTime;

    @Schema(description = "请设置每多少千克获得一次基金")
    private BigDecimal eachEmission;

    @Schema(description = "请设置每次满足减排量后获得基金")
    private BigDecimal eachEmissionFund;

    @Schema(description = "请设置1克等于多少分")
    private BigDecimal oneKgPoints;

    public BigDecimal getCarbonEmissionLimit() {
        if (carbonEmissionLimit == null) {
            carbonEmissionLimit = BigDecimal.ZERO;
        }
        return carbonEmissionLimit.multiply(BigDecimal.valueOf(1000));
    }
}
