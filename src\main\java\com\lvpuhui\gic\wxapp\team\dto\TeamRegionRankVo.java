package com.lvpuhui.gic.wxapp.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Schema(description = "地域排名返回值")
public class TeamRegionRankVo {

    @Schema(description = "地域code")
    private String regionCode;

    @Schema(description = "地域名")
    private String regionName;

    @Schema(description = "排名")
    private Integer rank;

    @Schema(description = "减排量")
    private BigDecimal emission;
}
