package com.lvpuhui.gic.wxapp.alipay.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class AlipayUserAuthData {
    private String alipayUserId;
    private String accessToken;
    private int expiresIn;
    private String refreshToken;
    private int reExpiresIn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date authStart;

    public String getAlipayUserId() {
        return alipayUserId;
    }

    public void setAlipayUserId(String alipayUserId) {
        this.alipayUserId = alipayUserId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public int getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(int expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public int getReExpiresIn() {
        return reExpiresIn;
    }

    public void setReExpiresIn(int reExpiresIn) {
        this.reExpiresIn = reExpiresIn;
    }

    public Date getAuthStart() {
        return authStart;
    }

    public void setAuthStart(Date authStart) {
        this.authStart = authStart;
    }
}
