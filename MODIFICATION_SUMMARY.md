# 第二阶段检查功能修改总结

## 修改概述

根据需求，成功实现了第二阶段检查功能，主要特点：

1. **进入第二阶段条件**：减排量达到80KG 或 活动结束时间到达
2. **基金发放规则**：进入第二阶段后，每增加10KG减排量发放一次基金
3. **补发机制**：支持一次性补发多次基金
4. **避免else语句**：所有方法都使用return提前返回，避免else嵌套

## 核心业务逻辑

### 场景A：通过减排量进入第二阶段
- 用户A在80KG时进入第二阶段
- 当减排量达到105KG时，基于进入时的80KG计算：105-80=25KG，发放2次基金
- 如果用户后续减排量达到120KG，基于上次发放时的105KG计算：120-105=15KG，发放1次基金

### 场景B：通过活动结束时间进入第二阶段
- 用户B在活动结束时减排量为50KG，此时进入第二阶段
- 当减排量达到75KG时，基于进入时的50KG计算：75-50=25KG，发放2次基金

### 场景C：参数调整的兼容性
- 用户C之前在间隔10KG时发放过基金，上次发放时减排量为100KG
- 现在间隔改为5KG，当前减排量107KG
- 基于上次发放时的100KG计算：107-100=7KG，按新间隔5KG发放1次基金

## 文件修改详情

### 1. 枚举类修改

#### DataRecordType.java
```java
// 添加新枚举值
PASS_FIRST_STAGE(3,"通过第一阶段"),
```

#### DonationType.java
```java
// 添加新枚举值
FUND(2, "基金");
```

### 2. DAO层修改

#### GlBehaviorDao.java
```java
// 添加根据时间条件查询减排量的方法
@Select("SELECT SUM(emission) FROM gl_behavior WHERE mobile_sha256 = #{mobileSha256} AND created >= #{startTime}")
BigDecimal getSumEmissionByMobileSha256AndCreatedAfter(@Param("mobileSha256") String mobileSha256, @Param("startTime") Date startTime);
```

#### DonationDao.java
```java
// 删除了之前添加的统计方法（不再需要）
// int countByMobileSha256AndType(@Param("mobileSha256") String mobileSha256, @Param("type") Integer type);
```

#### DonationDao.xml
```xml
<!-- 删除了之前添加的统计SQL（不再需要） -->
```

### 3. Service层修改

#### LowCarbonClockInService.java
```java
// 添加接口方法
void processSecondStageCheck(String mobileSha256);
```

#### LowCarbonClockInServiceImpl.java
主要添加了以下方法：

1. **processSecondStageCheck** - 主入口方法
2. **handleFirstTimeSecondStageCheck** - 处理首次进入第二阶段
3. **handleExistingSecondStageCheck** - 处理已进入第二阶段的情况
4. **getEntryEmissionFromDataRecord** - 解析进入时减排量
5. **recordPassFirstStageWithEmission** - 记录进入第二阶段
6. **getLatestFundDonation** - 获取最新基金发放记录
7. **checkAndIssueFunds** - 检查并发放基金（重写了逻辑）
8. **issueFundMultipleTimes** - 批量发放基金

## 关键技术实现

### 1. 数据存储格式
- **gl_data_record.dataJson**：`{"emissionAtEntry": "85.5"}`
- **gl_donation.notes**：`"95.8"`

### 2. 基金发放算法（重要修改）
```java
// 确定基准减排量（关键改进）
BigDecimal baseEmissionKg = entryEmissionKg; // 默认使用进入时减排量
Donation latestFundDonation = getLatestFundDonation(mobileSha256);
if (Objects.nonNull(latestFundDonation) && StrUtil.isNotBlank(latestFundDonation.getNotes())) {
    baseEmissionKg = new BigDecimal(latestFundDonation.getNotes()); // 使用上次发放时减排量
}

// 计算应发放次数
BigDecimal emissionDiff = currentEmissionKg.subtract(baseEmissionKg);
int shouldIssueTimes = emissionDiff.divide(FUND_EMISSION_INTERVAL, 0, RoundingMode.DOWN).intValue();
```

### 3. 事务处理
- 使用 `@Transactional(rollbackFor = Exception.class)` 确保数据一致性
- 所有数据库操作都有异常处理和回滚机制

### 4. 代码风格
- 避免使用else语句，使用return提前返回
- 详细的日志记录便于问题排查
- 完善的异常处理机制

## 测试覆盖

创建了完整的单元测试，覆盖以下场景：
1. 首次检查且减排量满足条件
2. 首次检查且减排量不足且活动未结束
3. 已进入第二阶段且应该发放基金
4. 已进入第二阶段但不应该发放基金

## 待配置项

以下参数需要后续配置：
1. **活动结束时间**：当前使用 `new Date()`，需要从配置中获取
2. **基金金额**：当前使用固定值100，需要实现计算规则
3. **减排量阈值**：当前硬编码80KG和10KG，可考虑配置化

## 部署注意事项

1. 确保数据库表结构支持新的枚举值
2. 测试现有数据的兼容性
3. 监控基金发放的准确性
4. 建议先在测试环境验证完整流程

## 性能考虑

1. 基金发放采用批量插入，减少数据库交互
2. 查询操作都有适当的索引支持
3. 事务范围控制在合理范围内
4. 日志记录适度，避免过多I/O操作
