package com.lvpuhui.gic.wxapp.team.dto;

import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

@Data
@Schema(description = "本地战队排名返回值")
public class TeamRankRegionListVo {

    /**
     * 战队ID
     */
    @Schema(description = "战队ID")
    private Long teamId;

    /**
     * 排行榜
     */
    @Schema(description = "排行榜")
    private Long rank;

    /**
     * 减排量
     */
    @Schema(description = "减排量")
    private BigDecimal emission;

    /**
     * 减排量-文本
     */
    @Schema(description = "减排量-文本")
    private String emissionText;

    /**
     * 战队名称
     */
    @Schema(description = "战队名称")
    private String teamName;

    /**
     * 战队Logo
     */
    @Schema(description = "战队Logo")
    private String teamLogo;

    public String getEmissionText() {
        if(Objects.nonNull(emission)){
            return CalcUtil.weightFormat(emission.doubleValue());
        }
        return "0g";
    }
}
