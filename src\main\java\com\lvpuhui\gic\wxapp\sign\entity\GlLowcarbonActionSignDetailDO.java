package com.lvpuhui.gic.wxapp.sign.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 低碳小事签到明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_lowcarbon_action_sign_detail")
public class GlLowcarbonActionSignDetailDO extends Model<GlLowcarbonActionSignDetailDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 签到表
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户手机号hash
     */
    private String mobileSha256;

    /**
     * 类型（0每日签到,1补签）
     */
    private Integer type;

    /**
     * 低碳小事id
     */
    private Integer actionId;

    /**
     * 签到日期
     */
    private LocalDate signDate;

    /**
     * 积分
     */
    @TableField("`point`")
    private Integer point;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
