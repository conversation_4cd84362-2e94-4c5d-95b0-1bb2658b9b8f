package com.lvpuhui.gic.wxapp.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.entity.vo.*;
import com.lvpuhui.gic.wxapp.service.CarbonBookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
//@RestController
//@RequestMapping("")
@Slf4j
public class CarbonBookController {

    @Resource
    CarbonBookService carbonBookService;
    /**
     * 碳账本
     * GET /carbon_books
     * 接口ID：18720037
     * 接口地址：https://www.apifox.cn/web/project/929917/apis/api-18720037
     * @param mobileSha256
     * @return
     */
    @RequestMapping("/carbon_books")
    public R<CarbonBooksVo> carbonBooks(String mobileSha256){
        return R.ok(carbonBookService.getCarbonBooks(mobileSha256));
    }

    /**
     * 减排明细
     * GET /emission_scene_details
     * 接口ID：18754239
     * 接口地址：https://www.apifox.cn/web/project/929917/apis/api-18754239
     * @param mobileSha256
     * @return
     */
    @RequestMapping("/emission_scene_details")
    public R<SceneTotalVo> emissionSceneDetails(String mobileSha256, Integer sceneId, Integer page, Integer size){
        if(null == page){
            page =1;
        }
        if(null == size){
            size =20;
        }
        return R.ok(carbonBookService.getSceneDetails(mobileSha256,sceneId,page,size));
    }

    /**
     * 获取我的累计减排量
     * GET /my_emission
     * 接口ID：18797289
     * 接口地址：https://www.apifox.cn/web/project/929917/apis/api-18797289
     * @param mobileSha256
     * @return
     */
    @RequestMapping("/my_emission")
    public R<PointsEmission> myEmission(String mobileSha256){
        return R.ok(carbonBookService.myPointsEmission(mobileSha256));
    }

    /**
     * 碳账本接口
     */
    @GetMapping("/carbon_books_mobile")
    public R<CarbonBooksNewVo> carbonBooksMobile(){
        return R.ok(carbonBookService.carbonBooksMobile());
    }

    /**
     * 碳账本减排明细接口
     */
    @GetMapping("/carbon_books_mobile_detail")
    public R<CarbonBooksDetailNewVo> carbonBooksMobileDetail(Integer sceneId, Integer page, Integer size){
        return R.ok(carbonBookService.carbonBooksMobileDetail(sceneId,page,size));
    }
}
