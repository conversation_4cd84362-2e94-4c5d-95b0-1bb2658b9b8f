# 批量基金发放功能实现总结

## 概述
新增了批量基于减排量的基金发放功能，支持断点续传和增量处理，同时改造了原有的基金发放方法以支持数据溯源。

## 修改内容

### 1. 枚举类修改
#### DataRecordType.java
```java
FUND_PROGRESS(4,"基金发放进度"), // 新增
```

### 2. 接口方法
#### LowCarbonClockInService.java
```java
// 新增批量处理方法
void processBatchEmissionFund(String mobileSha256);

// 修改原有方法，添加detailId参数
void processEmissionBasedFund(String mobileSha256, BigDecimal emission, Long detailId);
```

### 3. 实现类方法
#### LowCarbonClockInServiceImpl.java

##### 主要方法
- `processBatchEmissionFund(String mobileSha256)` - 批量基金发放主入口
- `processEmissionBasedFund(String, BigDecimal, Long)` - 修改后的单次基金发放

##### 新增私有方法
- `getLatestFundProgress()` - 获取最新发放进度
- `queryAllClockInDetails()` - 查询所有打卡详情
- `queryIncrementalClockInDetails()` - 查询增量打卡详情
- `recordFundProgress()` - 记录发放进度
- `processEmissionBasedFundForBatch()` - 批量处理中的单次发放
- `handleEnterSecondStageCheckForBatch()` - 批量处理中的第二阶段检查
- `recordEnterSecondStageForBatch()` - 批量处理中的第二阶段记录
- `issueEmissionBasedFundForBatch()` - 批量处理中的基金发放

## 核心业务逻辑

### 批量发放流程
1. **查询发放进度**：检查是否有之前的发放记录
2. **数据查询**：
   - 首次发放：查询所有 `source=1` 的打卡详情
   - 增量发放：查询 `id > lastDetailId` 的新增数据
3. **循环发放**：对每条记录调用基金发放逻辑
4. **记录进度**：保存最后处理的记录ID

### 断点续传机制
- 通过 `gl_data_record` 表记录发放进度
- 支持从上次中断的地方继续处理
- 避免重复发放基金

### 数据溯源
- `gl_donation.notes` 格式：`"detailId,emission"`
- 可以追溯到具体的打卡详情记录

## 数据存储

### gl_data_record表（类型4）
- **type**: 4 (FUND_PROGRESS)
- **dataJson**: `{"lastDetailId": "12345"}` - 最后处理的详情ID

### gl_donation表
- **notes**: `"123,5000"` - detailId,减排量（逗号分隔）

### 查询条件
- **gl_low_carbon_clock_in_details**: `mobileSha256` + `source=1` + `id排序`

## 关键特性

### 1. 事务处理
- 整个批量处理使用一个事务
- 确保数据一致性

### 2. 增量处理
- 支持断点续传
- 只处理新增的数据

### 3. 数据溯源
- 每笔基金都能追溯到具体的打卡记录
- 便于数据审计和问题排查

### 4. 日志记录
- 详细的处理日志
- 便于监控和问题定位

## 使用示例

```java
@Autowired
private LowCarbonClockInService lowCarbonClockInService;

// 批量处理基金发放
public void batchProcessFund(String mobileSha256) {
    try {
        lowCarbonClockInService.processBatchEmissionFund(mobileSha256);
        log.info("批量基金发放处理完成，用户: {}", mobileSha256);
    } catch (Exception e) {
        log.error("批量基金发放处理失败，用户: {}", mobileSha256, e);
    }
}

// 单次基金发放（需要detailId）
public void singleProcessFund(String mobileSha256, BigDecimal emission, Long detailId) {
    try {
        lowCarbonClockInService.processEmissionBasedFund(mobileSha256, emission, detailId);
        log.info("单次基金发放处理完成");
    } catch (Exception e) {
        log.error("单次基金发放处理失败", e);
    }
}
```

## 执行流程图

```
processBatchEmissionFund
├── getLatestFundProgress (查询进度)
├── 判断首次/增量
│   ├── queryAllClockInDetails (首次)
│   └── queryIncrementalClockInDetails (增量)
├── 循环处理每条记录
│   └── processEmissionBasedFundForBatch
│       ├── 检查第二阶段状态
│       ├── 判断进入条件
│       └── 发放基金
└── recordFundProgress (记录进度)
```

## 注意事项

1. **数据完整性**：整个批量处理是一个事务
2. **性能考虑**：大量数据时需要考虑分批处理
3. **重复处理**：通过进度记录避免重复发放
4. **数据溯源**：notes字段格式必须严格遵循 "detailId,emission"
5. **异常处理**：任何异常都会回滚整个批量处理

## 编译状态

✅ 所有代码编译通过，无错误和警告（除TODO注释外）

## 待优化项

1. **活动结束时间**：需要从配置中获取
2. **批量大小**：考虑添加分页处理大量数据
3. **性能监控**：添加处理时间和数据量的监控
