package com.lvpuhui.gic.wxapp.team.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.team.dto.*;
import com.lvpuhui.gic.wxapp.team.service.TeamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 战队服务
 * <AUTHOR>
 * @since 2023年05月24日 15:03:00
 */
@RestController
@RequestMapping("/team")
@Tags(value = {@Tag(name = "战队API 1.0")})
public class TeamController {

    @Resource
    private TeamService teamService;

    /**
     * 我的战队信息接口
     */
    @GetMapping("/me_team")
    public R<TeamMeVo> meTeam(){
        TeamMeVo meTeamVo = teamService.meTeam();
        return R.ok(meTeamVo);
    }

    /**
     * 创建战队接口
     */
    @PostMapping("/create_team")
    public R<String> createTeam(@RequestBody @Valid TeamCreateDto teamCreateDto){
        teamService.createTeam(teamCreateDto);
        return R.ok(null);
    }

    /**
     * 战队列表接口
     */
    @PostMapping("/team_list")
    public R<List<TeamListVo>> teamList(@RequestBody TeamListDto teamListDto){
        List<TeamListVo> teamListVos = teamService.teamList(teamListDto);
        return R.ok(teamListVos);
    }

    /**
     * 战队列表-战队详情接口
     */
    @GetMapping("/team_list_detail")
    public R<TeamListDetailVo> teamListDetail(@RequestParam("teamId") Long teamId){
        TeamListDetailVo teamListDetailVo = teamService.teamListDetail(teamId);
        return R.ok(teamListDetailVo);
    }

    /**
     * 我的战队详情接口
     */
    @GetMapping("/team_detail")
    public R<TeamDetailVo> teamDetail(){
        TeamDetailVo teamDetailVo = teamService.teamDetail();
        return R.ok(teamDetailVo);
    }

    /**
     * 战队排行榜接口
     */
    @GetMapping("/team_rank")
    public R<TeamRankVo> teamRank(){
        TeamRankVo teamRankVo = teamService.teamRank();
        return R.ok(teamRankVo);
    }

    /**
     * 对内排行榜接口
     */
    @GetMapping("/team_member_rank")
    public R<TeamMemberRankVo> teamMemberRank(){
        TeamMemberRankVo teamMemberRankVo = teamService.teamMemberRank();
        return R.ok(teamMemberRankVo);
    }

    /**
     * 加入战队接口
     */
    @PostMapping("/team_join")
    public R<String> teamJoin(@RequestBody @Valid TeamJoinDto teamJoinDto){
        teamService.teamJoin(teamJoinDto);
        return R.ok(null);
    }

    /**
     * 退出战队接口
     */
    @GetMapping("/team_exit")
    public R<String> teamExit(@RequestParam("teamId") Long teamId){
        teamService.teamExit(teamId);
        return R.ok(null);
    }

    /**
     * 修改战队接口
     */
    @PostMapping("/update_team")
    public R<String> updateTeam(@RequestBody @Valid TeamUpdateDto teamUpdateDto){
        teamService.updateTeam(teamUpdateDto);
        return R.ok(null);
    }

    @Operation(summary = "地域排名接口", description = "地域排名接口")
    @GetMapping("/regionRank")
    public R<List<TeamRegionRankVo>> regionRank(){
        List<TeamRegionRankVo> teamRegionRankVos = teamService.regionRank();
        return R.ok(teamRegionRankVos);
    }

    @Operation(summary = "地域集合接口", description = "地域集合接口")
    @GetMapping("/regionList")
    public R<List<TeamRegionListVo>> regionList(){
        List<TeamRegionListVo> teamRegionListVos = teamService.regionList();
        return R.ok(teamRegionListVos);
    }

    @Operation(summary = "本地战队排名接口", description = "本地战队排名接口")
    @GetMapping("/team_region_rank")
    public R<TeamRankRegionVo> teamRegionRank(){
        TeamRankRegionVo teamRankRegionVo = teamService.teamRegionRank();
        return R.ok(teamRankRegionVo);
    }
}
