<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.sign.dao.GlLowcarbonActionSignDetailDao">

    <select id="getIsExit" resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionSignDetailDO">
        select
        id,
        user_id as userId,
        mobile_sha256 as mobileSha256,
        `type`,
        action_id as actionId,
        sign_date as signDate,
        `point`,
        updated_at as updatedAt,
        created_at as createdAt
        from gl_lowcarbon_action_sign_detail where user_id = #{userId} and sign_date = #{today}
    </select>
    <select id="selectIsSupple" resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionSignDetailDO">
        select
        id,
        user_id as userId,
        mobile_sha256 as mobileSha256,
        `type`,
        action_id as actionId,
        sign_date as signDate,
        `point`,
        updated_at as updatedAt,
        created_at as createdAt
        from gl_lowcarbon_action_sign_detail where user_id = #{userId} and `type` = #{type}
    </select>
    <select id="getDateIsExit" resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionSignDetailDO">
        select
        id,
        user_id as userId,
        mobile_sha256 as mobileSha256,
        `type`,
        action_id as actionId,
        sign_date as signDate,
        `point`,
        updated_at as updatedAt,
        created_at as createdAt
        from gl_lowcarbon_action_sign_detail where user_id = #{userId} and sign_date = #{format}
    </select>
    <select id="selectByUserAndLocal"
            resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionSignDetailDO">
        select
        id,
        user_id as userId,
        mobile_sha256 as mobileSha256,
        `type`,
        action_id as actionId,
        sign_date as signDate,
        `point`,
        updated_at as updatedAt,
        created_at as createdAt
        from gl_lowcarbon_action_sign_detail where user_id = #{userId} and sign_date = #{today}
    </select>
    <select id="selectByUserId" resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionSignDetailDO">
        select
        id,
        user_id as userId,
        mobile_sha256 as mobileSha256,
        `type`,
        action_id as actionId,
        sign_date as signDate,
        `point`,
        updated_at as updatedAt,
        created_at as createdAt
        from gl_lowcarbon_action_sign_detail where user_id = #{userId}
    </select>
</mapper>
