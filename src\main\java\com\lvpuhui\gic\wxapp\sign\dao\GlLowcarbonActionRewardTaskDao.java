package com.lvpuhui.gic.wxapp.sign.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionRewardTaskDO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 连续签到奖励任务周期表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
public interface GlLowcarbonActionRewardTaskDao extends BaseMapper<GlLowcarbonActionRewardTaskDO> {

    GlLowcarbonActionRewardTaskDO selectByUserId(@Param("userId") Long userId);
}
