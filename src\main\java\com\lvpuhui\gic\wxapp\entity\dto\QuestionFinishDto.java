package com.lvpuhui.gic.wxapp.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 问卷完成DTO
 * <AUTHOR>
 * @since 2022年6月1日 16:45:04
 */
@Data
public class QuestionFinishDto {

    /**
     * 微信openId
     */
    private String openId;

    /**
     * 用户id(sha256)
     */
    @NotBlank(message = "缺少用户标识")
    private String mobileSha256;

    /**
     * 请您先选择填写的问卷
     */
    @NotNull(message = "请您先选择填写的问卷")
    private Long appletQuestionId;

    /**
     * 问卷id
     */
    @NotNull(message = "请您先填写问卷")
    private String questionId;

    /**
     * 问卷记录id
     */
    @NotNull(message = "请您先填写问卷")
    private Long questionRecordId;
}
