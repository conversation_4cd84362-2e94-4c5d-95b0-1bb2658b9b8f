<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.medal.dao.MedalDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.medal.entity.Medal">
    <!--@mbg.generated-->
    <!--@Table gl_medal-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="introduce" jdbcType="VARCHAR" property="introduce" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="clock_in" jdbcType="INTEGER" property="clockIn" />
    <result column="not_obtained_icon" jdbcType="VARCHAR" property="notObtainedIcon" />
    <result column="obtained_icon" jdbcType="VARCHAR" property="obtainedIcon" />
    <result column="emission" jdbcType="DECIMAL" property="emission" />
    <result column="carbon_points" jdbcType="INTEGER" property="carbonPoints" />
    <result column="share_number" jdbcType="INTEGER" property="shareNumber" />
    <result column="tickoff_number" jdbcType="INTEGER" property="tickoffNumber" />
    <result column="need_team" jdbcType="INTEGER" property="needTeam" />
    <result column="friend_number" jdbcType="INTEGER" property="friendNumber" />
    <result column="likes_number" jdbcType="INTEGER" property="likesNumber" />
    <result column="liked_number" jdbcType="INTEGER" property="likedNumber" />
    <result column="clock_in_sum_number" jdbcType="INTEGER" property="clockInSumNumber" />
    <result column="invite_number" jdbcType="INTEGER" property="inviteNumber" />
    <result column="grant_points" jdbcType="INTEGER" property="grantPoints" />
    <result column="hidden_attribute" jdbcType="TINYINT" property="hiddenAttribute" />
    <result column="sequence" jdbcType="INTEGER" property="sequence" />
    <result column="obtain_number" jdbcType="INTEGER" property="obtainNumber" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="updated" jdbcType="TIMESTAMP" property="updated" />
    <result column="updator" jdbcType="BIGINT" property="updator" />
    <result column="deleted" jdbcType="BOOLEAN" property="deleted" />
    <result column="state" jdbcType="INTEGER" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, introduce, description, clock_in, not_obtained_icon, obtained_icon, emission, carbon_points,
    share_number, tickoff_number, need_team, friend_number, likes_number, liked_number, 
    clock_in_sum_number, invite_number, grant_points, hidden_attribute, `sequence`, obtain_number, 
    created, creator, updated, updator, deleted, `state`
  </sql>

  <update id="updateAddObtainNumber">
    update gl_medal set obtain_number = obtain_number + 1 where id = #{id}
  </update>
</mapper>