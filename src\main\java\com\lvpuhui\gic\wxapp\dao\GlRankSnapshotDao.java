package com.lvpuhui.gic.wxapp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.entity.GlRankSnapshot;
import com.lvpuhui.gic.wxapp.entity.po.RankAccumulateList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 排行榜快照表 Mapper 接口
 * <AUTHOR>
 * @since 2022-07-25
 */
public interface GlRankSnapshotDao extends BaseMapper<GlRankSnapshot> {

    /**
     * 获取前20累积排行
     */
    @Select("SELECT " +
            "gr.`rank`, " +
            "gr.emission, " +
            "gu.nick_name, " +
            "gu.avatar_url, " +
            "gu.mobile_sha256 " +
            "FROM " +
            "gl_rank_snapshot gr " +
            "INNER JOIN gl_user gu ON gr.mobile_sha256 = gu.mobile_sha256 where gr.`rank` > 0 " +
            "ORDER BY gr.`rank` " +
            "LIMIT #{top}")
    List<RankAccumulateList.AccumulateRankUser> getSnapshotRankUserInfo(@Param("top") Integer top);

    /**
     * 根据用户查询具体的排行
     */
    @Select("SELECT " +
            "gr.`rank`, " +
            "gr.city_rank, " +
            "gr.emission, " +
            "gu.nick_name, " +
            "gu.avatar_url, " +
            "gu.mobile_sha256 " +
            "FROM " +
            "gl_rank_snapshot gr " +
            "INNER JOIN gl_user gu ON gr.mobile_sha256 = gu.mobile_sha256  " +
            "WHERE gr.mobile_sha256 = #{mobileSha256}")
    RankAccumulateList.AccumulateRankUser getRankUserInfoBySha256(@Param("mobileSha256") String mobileSha256);
}
