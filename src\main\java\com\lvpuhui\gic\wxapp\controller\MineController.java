package com.lvpuhui.gic.wxapp.controller;

import com.baomidou.mybatisplus.extension.api.ApiController;
import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.alipay.dto.AuthCodeDTO;
import com.lvpuhui.gic.wxapp.alipay.serivice.AuthService;
import com.lvpuhui.gic.wxapp.entity.dto.CodeOpenidDto;
import com.lvpuhui.gic.wxapp.entity.dto.FriendsDto;
import com.lvpuhui.gic.wxapp.entity.dto.TokenValidDto;
import com.lvpuhui.gic.wxapp.entity.dto.UserAuthDto;
import com.lvpuhui.gic.wxapp.entity.po.CodeOpenid;
import com.lvpuhui.gic.wxapp.entity.po.CompanyRank;
import com.lvpuhui.gic.wxapp.entity.po.UserInfo;
import com.lvpuhui.gic.wxapp.entity.vo.ShareUserInfoVo;
import com.lvpuhui.gic.wxapp.infrastructure.interceptor.PassToken;
import com.lvpuhui.gic.wxapp.service.GlRankCompanyService;
import com.lvpuhui.gic.wxapp.service.GlUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 小程序我的-服务
 * <AUTHOR>
 * @since 2022年05月07日 14:26:00
 */
@Tags(value = {@Tag(name = "用户接口1.0")})
@RestController
public class MineController extends ApiController {

    @Autowired
    private GlUserService glUserService;

    @Resource
    private GlRankCompanyService glRankCompanyService;

    @Resource
    private AuthService alipayAuthService;

    /**
     * 获取token是否有效
     */
    @PassToken
    @GetMapping("/token_valid")
    public R<TokenValidDto> getTokenValid(HttpServletRequest request){
        TokenValidDto tokenValidDto = glUserService.getTokenValid(request);
        return R.ok(tokenValidDto);
    }

    /**
     * 用户授权接口-lijianguang
     */
    @PostMapping("/user_auth")
    public R<UserInfo> userAuth(@RequestBody UserAuthDto userAuthDto){
        UserInfo userInfo = glUserService.userAuth(userAuthDto);
        return success(userInfo);
    }

    /**
     * 根据code获取openid和加密手机号接口-lijianguang
     */
    @PassToken
    @PostMapping("/code2session")
    public R<CodeOpenid> code2session(@RequestBody @Valid CodeOpenidDto codeOpenidDto){
        CodeOpenid codeOpenid = glUserService.code2session(codeOpenidDto);
        return success(codeOpenid);
    }

    /**
     * 根据authcode获取openid和加密手机号接口-lijianguang
     */
    @PassToken
    @PostMapping("/authtoken")
    public R<CodeOpenid> authAlipayToken(@RequestBody @Valid AuthCodeDTO authCodeDTO){
        CodeOpenid codeOpenid = alipayAuthService.getAuthUser(authCodeDTO);
        return success(codeOpenid);
    }

    /**
     * 企业排行接口-lijianguang
     */
    @PassToken
    @GetMapping("/company_rank")
    public R<List<CompanyRank>> companyRank(){
        List<CompanyRank> companyRanks = glRankCompanyService.companyRank();
        return R.ok(companyRanks);
    }

    /**
     * 用户授权手机号记录接口
     */
    @GetMapping("/auth_record")
    public R<String> authRecord(){
        glUserService.authRecord();
        return R.ok("success");
    }

    @Operation(summary = "好友关系接口", description = "好友关系接口", tags = { "用户接口1.0" })
    @PostMapping("/user_friends")
    public R<String> userFriends(@RequestBody FriendsDto friendsDto){
        glUserService.userFriends(friendsDto);
        return R.ok("success");
    }

    @GetMapping("/getShareUserInfo")
    public R<ShareUserInfoVo> getShareUserInfo(@RequestParam("mobileSha256") String mobileSha256){
        ShareUserInfoVo shareUserInfo = glUserService.getShareUserInfo(mobileSha256);
        return R.ok(shareUserInfo);
    }
}