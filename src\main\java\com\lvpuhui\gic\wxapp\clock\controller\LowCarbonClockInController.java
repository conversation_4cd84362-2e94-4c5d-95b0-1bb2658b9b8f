package com.lvpuhui.gic.wxapp.clock.controller;

import com.baomidou.mybatisplus.extension.api.ApiController;
import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.clock.dto.PointsObtainDto;
import com.lvpuhui.gic.wxapp.clock.dto.request.ClockInDto;
import com.lvpuhui.gic.wxapp.clock.dto.response.*;
import com.lvpuhui.gic.wxapp.clock.service.LowCarbonClockInService;
import com.lvpuhui.gic.wxapp.entity.vo.CertificateVo;
import com.lvpuhui.gic.wxapp.infrastructure.interceptor.PassToken;
import com.lvpuhui.gic.wxapp.service.GlAppletConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/lowCarbonClockIn")
@Tags(value = {@Tag(name = "低碳打卡API 1.0")})
public class LowCarbonClockInController extends ApiController {

    @Resource
    private LowCarbonClockInService lowCarbonClockInService;

    @Resource
    private GlAppletConfigService glAppletConfigService;

    @Operation(summary = "低碳打卡列表接口", description = "低碳打卡列表接口")
    @GetMapping("/list")
    @PassToken
    public R<List<LowCarbonClockInListVo>> list() {
        return success(lowCarbonClockInService.list());
    }

    @Operation(summary = "低碳打卡详情接口", description = "低碳打卡详情接口")
    @GetMapping("/detail/{id}")
    public R<LowCarbonClockInDetailVo> detail(@Schema(description = "低碳打卡ID") @PathVariable("id") Long id) {
        return success(lowCarbonClockInService.clockInDetail(id));
    }

    @Operation(summary = "低碳打卡明细接口", description = "低碳打卡明细接口")
    @GetMapping("/detail")
    public R<List<LowCarbonClockInDetailListVo>> detail(@Schema(description = "页码") @RequestParam("page") Integer page) {
        return success(lowCarbonClockInService.detail(page));
    }

    @Operation(summary = "用户信息接口", description = "用户信息接口")
    @GetMapping("/userInfo")
    @PassToken
    public R<UserInfoVo> userInfo(@RequestParam(value = "mobileSha256",required = false) String mobileSha256) {
        return success(lowCarbonClockInService.userInfo(mobileSha256));
    }

    @Operation(summary = "拍照打卡接口", description = "拍照打卡接口")
    @PostMapping("/clockIn")
    public R<BigDecimal> clockIn(@RequestBody @Valid ClockInDto clockInDto) {
        BigDecimal amount = lowCarbonClockInService.clockIn(clockInDto);
        return success(amount);
    }

    @Operation(summary = "判断是否有新的勋章接口", description = "判断是否有新的勋章接口")
    @GetMapping("/judgeNewMedal")
    public R<List<IsNewMedalVo>> judgeNewMedal() {
        List<IsNewMedalVo> isNewMedalVo = lowCarbonClockInService.judgeNewMedal();
        return success(isNewMedalVo);
    }

    @Operation(summary = "拍照打卡判断是否可打卡接口", description = "拍照打卡判断是否可打卡接口")
    @GetMapping("/clockInJudge")
    public R<String> clockInJudge(@Schema(description = "低碳打卡ID") @RequestParam("clockInId") Long clockInId) {
        boolean result = lowCarbonClockInService.clockInJudge(clockInId);
        return success(Boolean.toString(result));
    }

    @Operation(summary = "活动进度接口", description = "活动进度接口")
    @GetMapping("/activityProgress")
    @PassToken
    public R<ActivityProgressVo> activityProgress(@RequestParam(value = "mobileSha256",required = false) String mobileSha256) {
        return success(lowCarbonClockInService.activityProgress(mobileSha256));
    }

    @Operation(summary = "分享活动进度接口", description = "分享活动进度接口")
    @GetMapping("/shareProgress")
    public R<String> shareProgress() {
        lowCarbonClockInService.shareProgress();
        return success("");
    }

    @Operation(summary = "积分获得配置接口", description = "积分获得配置接口")
    @GetMapping(value="/getPointsObtain")
    @PassToken
    public R<PointsObtainDto> getPointsObtain() {
        PointsObtainDto pointsObtainDto = glAppletConfigService.getPointsObtain();
        return success(pointsObtainDto);
    }

    @Operation(summary = "证书接口", description = "证书接口")
    @GetMapping("/certificate")
    public R<CertificateVo> certificate(){
        return R.ok(lowCarbonClockInService.certificate());
    }

    @Operation(summary = "守护点击保存接口", description = "守护点击保存接口")
    @GetMapping("/guard")
    public R<String> guard(@RequestParam("id") Long id){
        lowCarbonClockInService.guard(id);
        return success("守护成功");
    }

    /**
     * 给后台管理服务调用
     */
    @PassToken
    @GetMapping("/processUserClockInMedal")
    public R<String> processUserClockInMedal(@RequestParam("secret") String secret,
                             @RequestParam(value = "mobileSha256",required = false) String mobileSha256) {
        lowCarbonClockInService.processUserClockInMedal(secret,mobileSha256);
        return success("");
    }

    /**
     * 给后台管理服务调用
     */
    @PassToken
    @GetMapping("/initMedal")
    public R<String> initMedal(@RequestParam("secret") String secret) {
        if("b3739ed89e5b19c323194ac181d483ef".equals(secret)){
            lowCarbonClockInService.resetMedals();
        }
        return success("");
    }

    @Operation(summary = "拍照打卡碳账本接口", description = "拍照打卡碳账本接口")
    @GetMapping("/carbonBook")
    public R<CarbonBookVo> carbonBook(){
        CarbonBookVo carbonBookVo = lowCarbonClockInService.carbonBook();
        return success(carbonBookVo);
    }

    @Operation(summary = "拍照打卡碳账本详情接口", description = "拍照打卡碳账本详情接口")
    @GetMapping("/carbonBookDetail")
    public R<CarbonBookDetailCountVo> carbonBookDetail(@RequestParam("id") Long id,@RequestParam("page") Integer page){
        CarbonBookDetailCountVo carbonBookDetailVos = lowCarbonClockInService.carbonBookDetail(id,page);
        return success(carbonBookDetailVos);
    }

    @GetMapping("/executorCheck")
    public R<String> executorCheck(@RequestParam("secret") String secret){
        lowCarbonClockInService.executorCheck(secret);
        return success("执行成功");
    }
}
