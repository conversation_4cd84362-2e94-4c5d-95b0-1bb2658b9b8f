package com.lvpuhui.gic.wxapp.team.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 战队成员排行表(GlTeamMemberRank)表实体类
 *
 * <AUTHOR>
 * @since 2023-05-24 11:24:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_team_member_rank")
public class GlTeamMemberRank extends Model<GlTeamMemberRank> {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 战队ID
     */
    private Long teamId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 排行榜
     */
    @TableField(value = "`rank`")
    private Long rank;

    /**
     * 减排量
     */
    private BigDecimal emission;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}

