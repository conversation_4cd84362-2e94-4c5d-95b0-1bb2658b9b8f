package com.lvpuhui.gic.wxapp.sign.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 任务奖励发放表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_lowcarbon_action_reward")
public class GlLowcarbonActionRewardDO extends Model<GlLowcarbonActionRewardDO> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户手机号hash
     */
    private String mobileSha256;

    /**
     * 奖励类型（1连签7，2连签14，3连签21）
     */
    private Integer type;

    /**
     * 0待领取，1已领取,2失效
     */
    private Integer state;

    /**
     * 领取日期
     */
    private LocalDateTime receiveDate;

    /**
     * 奖励日期
     */
    private LocalDate rewardDate;

    /**
     * 连续奖励积分数
     */
    @TableField("`point`")
    private Integer point;

    /**
     * 任务周期数据快照记录
     */
    private String data;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
