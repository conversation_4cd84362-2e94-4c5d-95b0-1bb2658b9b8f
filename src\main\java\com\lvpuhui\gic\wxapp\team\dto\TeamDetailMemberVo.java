package com.lvpuhui.gic.wxapp.team.dto;

import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 我的战队详情
 * <AUTHOR>
 * @since 2023年05月24日 15:51:00
 */
@Data
public class TeamDetailMemberVo {

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatarUrl;

    /**
     * 减排量
     */
    private BigDecimal emission;

    /**
     * 减排量-文本
     */
    private String emissionText;

    public String getEmissionText() {
        if(Objects.nonNull(emission)){
            return CalcUtil.weightFormatEnglish(emission.doubleValue());
        }
        return "0g";
    }
}
