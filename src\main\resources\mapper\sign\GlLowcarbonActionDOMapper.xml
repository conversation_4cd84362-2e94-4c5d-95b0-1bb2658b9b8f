<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.sign.dao.GlLowcarbonActionDao">

    <select id="selectByCateId" resultType="com.lvpuhui.gic.wxapp.sign.vo.TrifleVo">
        select
        a.id as id,
        a.title as title,
        a.description as description,
        a.icon_light as iconLight,
        b.title as classTitle
        from
        gl_lowcarbon_action as a left join gl_lowcarbon_action_cate as b
        on a.cate_id = b.id
        where a.cate_id = #{cateId} and a.is_show = 0
    </select>
</mapper>
