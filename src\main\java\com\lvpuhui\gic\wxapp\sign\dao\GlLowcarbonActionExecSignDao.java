package com.lvpuhui.gic.wxapp.sign.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionExecSignDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 低碳小事践行签到表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
public interface GlLowcarbonActionExecSignDao extends BaseMapper<GlLowcarbonActionExecSignDO> {

    GlLowcarbonActionExecSignDO getTodayData(@Param("userId") Long userId, @Param("trifleId") Integer trifleId, @Param("today") LocalDate today);

    List<GlLowcarbonActionExecSignDO> selectByUserAndTri(@Param("userId")Long userId, @Param("actionId")Integer actionId);

    List<String> getMyLight(@Param("mobileSha256") String mobileSha256);
}
