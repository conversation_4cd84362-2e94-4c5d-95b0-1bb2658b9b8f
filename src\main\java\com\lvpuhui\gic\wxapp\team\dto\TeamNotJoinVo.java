package com.lvpuhui.gic.wxapp.team.dto;

import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 未加入战队信息
 * <AUTHOR>
 * @since 2023年05月24日 16:47:00
 */
@Data
public class TeamNotJoinVo {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 减排量
     */
    private BigDecimal emission;

    /**
     * 减排量-文本
     */
    private String emissionText;

    /**
     * 创建人昵称
     */
    private String nickName;

    /**
     * 创建人头像
     */
    private String avatarUrl;

    public String getEmissionText() {
        if(Objects.nonNull(emission)){
            return CalcUtil.weightFormat(emission.doubleValue());
        }
        return emissionText;
    }
}
