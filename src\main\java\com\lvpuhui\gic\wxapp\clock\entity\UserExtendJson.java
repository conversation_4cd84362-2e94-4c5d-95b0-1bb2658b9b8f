package com.lvpuhui.gic.wxapp.clock.entity;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class UserExtendJson {

    /**
     * 减排量(克)
     */
    private BigDecimal emission = BigDecimal.ZERO;

    /**
     * 勋章数量
     */
    private Integer medalNumber = 0;

    /**
     * 打卡次数json
     */
    private String clockInNumberJson;

    /**
     * 打卡总次数
     */
    private Integer sumClockInNumber = 0;
}
