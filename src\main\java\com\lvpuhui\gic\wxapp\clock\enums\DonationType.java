package com.lvpuhui.gic.wxapp.clock.enums;

import lombok.Getter;

@Getter
public enum DonationType {
    REDUCTION(0, "减排量配捐"),
    CONTRIBUTION_POINTS(1, "助力值(积分)配捐"),
    FUND(2, "基金"),
    SECOND_FUND(3, "第二基金-暂不使用");

    private final int code;
    private final String description;

    DonationType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static DonationType fromCode(int code) {
        for (DonationType type : DonationType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown donation type code: " + code);
    }
}
