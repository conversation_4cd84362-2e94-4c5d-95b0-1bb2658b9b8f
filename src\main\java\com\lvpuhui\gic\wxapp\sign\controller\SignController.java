package com.lvpuhui.gic.wxapp.sign.controller;


import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.sign.Exception.BizException;
import com.lvpuhui.gic.wxapp.sign.dto.*;
import com.lvpuhui.gic.wxapp.sign.service.SignService;
import com.lvpuhui.gic.wxapp.sign.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sign")
@Slf4j
public class SignController {


    @Resource
    private SignService signService;


    /**
     *分类小事列表
     * @return
     */
    @GetMapping("/signClassIfyTrifle")
    public R<SignClassifyTrifleListVo> signClassIfyTrifle(){
        SignClassifyTrifleListVo signClassifyTrifleListVo = signService.signClassIfyTrifle();
        return R.ok(signClassifyTrifleListVo);
    }

    /**
     * 践行签到
     */

    @PostMapping("/saveSign")
    public R<Integer> saveSign(@RequestBody SignSaveDto signSaveDto){
        try {
            Integer integer = signService.saveSign(signSaveDto);
            return R.ok(integer);
        }catch (BizException e){
            return R.failed(e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage());
            return R.failed("太快了，慢点");
        }
    }

    /**
     * 已点亮的低碳行为
     */
    @GetMapping("/litPicture")
    public R<List<LitPictureListVo>> litPicture(){
        List<LitPictureListVo> list = signService.litPicture();
        return R.ok(list);
    }

    /**
     * 补签
     */

    @PostMapping("/suppleSign")
    public R<Integer> suppleSign(@RequestBody SuppleSignSaveDto suppleSignSaveDto){
        try {
            Integer integer = signService.suppleSign(suppleSignSaveDto);
            return R.ok(integer);
        }catch (BizException e){
            return R.failed(e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage());
            return R.failed("操作太快，请稍后再试");
        }
    }

    /**
     * 获取签到列表
     */
    @PostMapping("/getSignList")
    public R<List<SignListVo>> getSignList(@RequestBody SignListDto signListDto){
        List<SignListVo> list = signService.getSignList(signListDto);
        return R.ok(list);
    }

    /**
     * 奖励列表
     */
    @GetMapping("/getIsReward")
    public R<GetIsRewardVo> getIsReward(){
        GetIsRewardVo setIsRewardVo = signService.getIsReward();
        return R.ok(setIsRewardVo);
    }

    /**
     * 获取连续奖励对应的积分
     */

    @GetMapping("/getRewardsPoints")
    public R<RewardPointsVo> getRewardsPoints(){
        RewardPointsVo rewardPointsVo = signService.getRewardsPoints();
        return R.ok(rewardPointsVo);
    }

    /**
     * 领取积分
     */
    @PostMapping("/getMyPoints")
    public R getMyPoints(@RequestBody SupplePointsDto supplePointsDto){
        try {
            signService.getMyPoints(supplePointsDto);
            return R.ok("领取成功");
        }catch (Exception e){
            log.error(e.getMessage());
            return R.failed("领取失败");
        }
    }

    /**
     * 获取连续签到天数以及积分
     */

    @GetMapping("/getAllSuppleDaysAndPoints")
    public R<PointsAndDaysVo> getAllSuppleDaysAndPoints(){
        PointsAndDaysVo pointsAndDaysVo = signService.getAllSuppleDaysAndPoints();
        return R.ok(pointsAndDaysVo);
    }

    @Operation(summary = "我的小事徽章接口", description = "我的小事徽章接口")
    @GetMapping("/myLight")
    public R<MySignVo> myLight(){
        return R.ok(signService.myLight());
    }

}
