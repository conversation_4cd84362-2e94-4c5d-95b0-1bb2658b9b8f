<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.sign.dao.GlLowcarbonActionRewardDao">


    <select id="selectByUserId" resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionRewardDO">
        select id,mobile_sha256 as mobileSha256,`type`,state,receive_date as receiveDate,reward_date as rewardDate,`point`,`data`,updated_at as updatedAt,created_at as createdAt from gl_lowcarbon_action_reward where mobile_sha256 =#{mobieShare}
    </select>
    <select id="selectByUserIdAndType"
            resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionRewardDO">
        select id,mobile_sha256 as mobileSha256,`type`,state,receive_date as receiveDate,reward_date as rewardDate,`point`,`data`,updated_at as updatedAt,created_at as createdAt from gl_lowcarbon_action_reward where mobile_sha256 =#{mobieShare} and `type` = 1 and state = 0
    </select>
    <select id="selectByUserIdAndfourteen"
            resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionRewardDO">
        select id,mobile_sha256 as mobileSha256,`type`,state,receive_date as receiveDate,reward_date as rewardDate,`point`,`data`,updated_at as updatedAt,created_at as createdAt from gl_lowcarbon_action_reward where mobile_sha256 =#{mobieShare} and `type` = 2 and state = 0
    </select>
    <select id="selectByUserIdAndTwenty"
            resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionRewardDO">
                select id,mobile_sha256 as mobileSha256,`type`,state,receive_date as receiveDate,reward_date as rewardDate,`point`,`data`,updated_at as updatedAt,created_at as createdAt from gl_lowcarbon_action_reward where mobile_sha256 =#{mobieShare} and `type` = 3 and state = 0
    </select>
</mapper>
