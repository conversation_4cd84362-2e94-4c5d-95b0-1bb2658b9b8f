package com.lvpuhui.gic.wxapp.team.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 战队修改参数
 * <AUTHOR>
 * @since 2023年05月29日 15:52:00
 */
@Data
public class TeamUpdateDto {

    /**
     * 战队ID
     */
    @NotNull(message = "请您正确修改战队")
    private Long teamId;

    /**
     * 战队名称
     */
    @NotBlank(message = "请您输入战队名称")
    private String teamName;

    /**
     * 战队Logo
     */
    @NotBlank(message = "请您上传战队LOGO")
    private String teamLogo;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 加入方式,无限制:0,邀请:1
     */
    @NotNull(message = "请您选择加入限制方式")
    private Integer joinMode;

    /**
     * 区域code
     */
    @NotBlank(message = "请选择战队区域")
    private String regionCode;

    /**
     * 区域名称
     */
    @NotBlank(message = "请选择战队区域")
    private String regionName;
}
