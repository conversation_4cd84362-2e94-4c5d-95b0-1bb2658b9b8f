<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.clock.dao.UserExtendDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.clock.entity.UserExtend">
    <!--@mbg.generated-->
    <!--@Table gl_user_extend-->
    <id column="mobile_sha256" jdbcType="VARCHAR" property="mobileSha256" />
    <result column="extend_json" jdbcType="VARCHAR" property="extendJson" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    mobile_sha256, extend_json
  </sql>
</mapper>