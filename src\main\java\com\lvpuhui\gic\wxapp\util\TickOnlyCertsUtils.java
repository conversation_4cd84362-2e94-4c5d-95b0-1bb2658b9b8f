package com.lvpuhui.gic.wxapp.util;

import cn.hutool.core.date.DatePattern;
import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.enums.TickoffType;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 打卡唯一标识工具类
 * <AUTHOR>
 * @since 2022年06月06日 17:06:00
 */
public class TickOnlyCertsUtils {

    public static List<String> generateOnlyCerts(Long userId, List<Long> typeIds, int type){
        List<String> onlyCertsList = Lists.newArrayListWithCapacity(typeIds.size());
        if(TickoffType.ONCE.getType() == type){
            for (Long typeId : typeIds) {
                String onlyCerts = userId + "$" + typeId;
                onlyCertsList.add(onlyCerts);
            }
            return onlyCertsList;
        }
        if(TickoffType.EVERY_DAY.getType() == type){
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN);
            String dateTime = dateTimeFormatter.format(LocalDateTime.now());
            for (Long typeId : typeIds) {
                String onlyCerts = userId + "$" + typeId + "$" + dateTime;
                onlyCertsList.add(onlyCerts);
            }
            return onlyCertsList;
        }
        // 每周和每月暂不处理
        return onlyCertsList;
    }

    public static String generateOnlyCerts(Long userId, Long typeId, int type){
        if(TickoffType.ONCE.getType() == type){
            return userId + "$" + typeId;
        }
        if(TickoffType.EVERY_DAY.getType() == type){
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN);
            String dateTime = dateTimeFormatter.format(LocalDateTime.now());
            return userId + "$" + typeId + "$" + dateTime;
        }
        // 每周和每月暂不处理
        return null;
    }
}
