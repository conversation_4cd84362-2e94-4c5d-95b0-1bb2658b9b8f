package com.lvpuhui.gic.wxapp.team.enums;

import lombok.Getter;

/**
 * 战队状态枚举
 * <AUTHOR>
 * @since 2023年05月25日 10:55:00
 */
@Getter
public enum TeamStatusEnum {

    ACTIVE(0,"正常"),
    IN_ACTIVE(1,"隐藏"),
    DELETED(-1,"已删除"),
    ;

    private Integer status;

    private String describe;

    TeamStatusEnum(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }
}
