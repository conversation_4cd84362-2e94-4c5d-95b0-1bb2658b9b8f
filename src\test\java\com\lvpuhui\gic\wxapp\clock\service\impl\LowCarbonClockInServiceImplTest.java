package com.lvpuhui.gic.wxapp.clock.service.impl;

import com.lvpuhui.gic.wxapp.clock.dao.DonationDao;
import com.lvpuhui.gic.wxapp.clock.entity.Donation;
import com.lvpuhui.gic.wxapp.clock.enums.DonationType;
import com.lvpuhui.gic.wxapp.common.dao.DataRecordDao;
import com.lvpuhui.gic.wxapp.common.entity.DataRecord;
import com.lvpuhui.gic.wxapp.common.enums.DataRecordType;
import com.lvpuhui.gic.wxapp.dao.GlBehaviorDao;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Date;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LowCarbonClockInServiceImpl的测试类
 * 主要测试processSecondStageCheck方法的各种场景
 */
@ExtendWith(MockitoExtension.class)
class LowCarbonClockInServiceImplTest {

    @Mock
    private DataRecordDao dataRecordDao;

    @Mock
    private DonationDao donationDao;

    @Mock
    private GlBehaviorDao glBehaviorDao;

    @InjectMocks
    private LowCarbonClockInServiceImpl lowCarbonClockInService;

    private final String TEST_MOBILE_SHA256 = "test_mobile_sha256";

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    void testProcessSecondStageCheck_FirstTime_EmissionSufficient() {
        // 测试首次检查且减排量满足条件的场景
        
        // Mock: 没有第二阶段记录
        when(dataRecordDao.selectOne(any())).thenReturn(null);
        
        // Mock: 用户总减排量为100KG（100000克）
        when(glBehaviorDao.getSumEmissionByMobileSha256(TEST_MOBILE_SHA256))
            .thenReturn(new BigDecimal("100000"));
        
        // Mock: 没有基金发放记录
        when(donationDao.selectOne(any())).thenReturn(null);
        
        // 执行测试
        lowCarbonClockInService.processSecondStageCheck(TEST_MOBILE_SHA256);
        
        // 验证：应该插入第二阶段记录
        verify(dataRecordDao, times(1)).insert(any(DataRecord.class));
        
        // 验证：应该发放基金（100KG进入，100-80=20KG，应该发放2次基金）
        verify(donationDao, times(2)).insert(any(Donation.class));
    }

    @Test
    void testProcessSecondStageCheck_FirstTime_EmissionInsufficient_ActivityNotEnded() {
        // 测试首次检查且减排量不足且活动未结束的场景
        
        // Mock: 没有第二阶段记录
        when(dataRecordDao.selectOne(any())).thenReturn(null);
        
        // Mock: 用户总减排量为50KG（50000克）
        when(glBehaviorDao.getSumEmissionByMobileSha256(TEST_MOBILE_SHA256))
            .thenReturn(new BigDecimal("50000"));
        
        // 执行测试
        lowCarbonClockInService.processSecondStageCheck(TEST_MOBILE_SHA256);
        
        // 验证：不应该插入第二阶段记录
        verify(dataRecordDao, never()).insert(any(DataRecord.class));
        
        // 验证：不应该发放基金
        verify(donationDao, never()).insert(any(Donation.class));
    }

    @Test
    void testProcessSecondStageCheck_ExistingStage_ShouldIssueFund() {
        // 测试已进入第二阶段且应该发放基金的场景
        
        // Mock: 已有第二阶段记录，进入时减排量为80KG
        DataRecord existingRecord = new DataRecord();
        existingRecord.setId(1L);
        existingRecord.setDataJson("{\"emissionAtEntry\":\"80\"}");
        when(dataRecordDao.selectOne(any())).thenReturn(existingRecord);
        
        // Mock: 有基金发放记录，上次发放时减排量为90KG
        Donation lastDonation = new Donation();
        lastDonation.setId(1L);
        lastDonation.setNotes("90"); // 上次发放时的减排量
        when(donationDao.selectOne(any())).thenReturn(lastDonation);
        
        // Mock: 用户当前总减排量为115KG（115000克）
        when(glBehaviorDao.getSumEmissionByMobileSha256(TEST_MOBILE_SHA256))
            .thenReturn(new BigDecimal("115000"));
        
        // 执行测试
        lowCarbonClockInService.processSecondStageCheck(TEST_MOBILE_SHA256);
        
        // 验证：应该发放基金（115-90=25KG，应该发放2次基金）
        verify(donationDao, times(2)).insert(any(Donation.class));
    }

    @Test
    void testProcessSecondStageCheck_ExistingStage_ShouldNotIssueFund() {
        // 测试已进入第二阶段但不应该发放基金的场景
        
        // Mock: 已有第二阶段记录，进入时减排量为80KG
        DataRecord existingRecord = new DataRecord();
        existingRecord.setId(1L);
        existingRecord.setDataJson("{\"emissionAtEntry\":\"80\"}");
        when(dataRecordDao.selectOne(any())).thenReturn(existingRecord);
        
        // Mock: 有基金发放记录，上次发放时减排量为90KG
        Donation lastDonation = new Donation();
        lastDonation.setId(1L);
        lastDonation.setNotes("90"); // 上次发放时的减排量
        when(donationDao.selectOne(any())).thenReturn(lastDonation);
        
        // Mock: 用户当前总减排量为95KG（95000克），新增只有5KG
        when(glBehaviorDao.getSumEmissionByMobileSha256(TEST_MOBILE_SHA256))
            .thenReturn(new BigDecimal("95000"));
        
        // 执行测试
        lowCarbonClockInService.processSecondStageCheck(TEST_MOBILE_SHA256);
        
        // 验证：不应该发放基金（新增减排量只有5KG，不足10KG）
        verify(donationDao, never()).insert(any(Donation.class));
    }

    @Test
    void testProcessSecondStageCheck_ExistingStage_NoFundRecord() {
        // 测试已进入第二阶段但没有基金发放记录的场景
        
        // Mock: 已有第二阶段记录，进入时减排量为80KG
        DataRecord existingRecord = new DataRecord();
        existingRecord.setId(1L);
        existingRecord.setDataJson("{\"emissionAtEntry\":\"80\"}");
        when(dataRecordDao.selectOne(any())).thenReturn(existingRecord);
        
        // Mock: 没有基金发放记录
        when(donationDao.selectOne(any())).thenReturn(null);
        
        // Mock: 用户当前总减排量为105KG（105000克）
        when(glBehaviorDao.getSumEmissionByMobileSha256(TEST_MOBILE_SHA256))
            .thenReturn(new BigDecimal("105000"));
        
        // 执行测试
        lowCarbonClockInService.processSecondStageCheck(TEST_MOBILE_SHA256);
        
        // 验证：应该发放基金（105-80=25KG，应该发放2次基金）
        verify(donationDao, times(2)).insert(any(Donation.class));
    }
}
