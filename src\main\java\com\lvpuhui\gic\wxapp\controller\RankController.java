package com.lvpuhui.gic.wxapp.controller;

import com.baomidou.mybatisplus.extension.api.ApiController;
import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.entity.dto.AccumulateObtainGoodsDto;
import com.lvpuhui.gic.wxapp.entity.dto.RankAccumulateListDto;
import com.lvpuhui.gic.wxapp.entity.dto.RankYesterdayListDto;
import com.lvpuhui.gic.wxapp.entity.dto.YesterdayObtainGoodsDto;
import com.lvpuhui.gic.wxapp.entity.po.*;
import com.lvpuhui.gic.wxapp.entity.vo.FriendsNumberRankVo;
import com.lvpuhui.gic.wxapp.entity.vo.RankFirstVo;
import com.lvpuhui.gic.wxapp.entity.vo.TodayRankVo;
import com.lvpuhui.gic.wxapp.infrastructure.interceptor.PassToken;
import com.lvpuhui.gic.wxapp.service.GlGoodsService;
import com.lvpuhui.gic.wxapp.service.GlRankService;
import com.lvpuhui.gic.wxapp.service.GlRankYesterdayService;
import com.lvpuhui.gic.wxapp.service.GlUserFriendsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 排行榜服务
 * <AUTHOR>
 * @since 2022年07月25日 18:07:00
 */
@RestController
@Tags(value = {@Tag(name = "排行API 1.0")})
public class RankController extends ApiController {

    @Resource
    private GlRankYesterdayService glRankYesterdayService;

    @Resource
    private GlRankService glRankService;

    @Resource
    private GlGoodsService glGoodsService;

    @Resource
    private GlUserFriendsService glUserFriendsService;

    /**
     * 昨日排行接口
     */
    @PostMapping("/yesterday_rank")
    public R<RankYesterdayList> yesterdayRank(@RequestBody @Valid RankYesterdayListDto rankYesterdayListDto){
        RankYesterdayList rankYesterdayList = glRankYesterdayService.yesterdayRank(rankYesterdayListDto);
        return R.ok(rankYesterdayList);
    }

    /**
     * 累积排行接口
     */
    @PostMapping("/accumulate_rank")
    public R<RankAccumulateList> accumulateRank(@RequestBody @Valid RankAccumulateListDto rankAccumulateListDto){
        RankAccumulateList rankAccumulateList = glRankService.accumulateRank(rankAccumulateListDto);
        return R.ok(rankAccumulateList);
    }
    @PostMapping("/invite_rank")
    public R<RankInviteList> rankInviteList(@RequestBody @Valid RankAccumulateListDto rankAccumulateListDto){
        RankInviteList rankInviteList = glRankService.getRankInviteList(rankAccumulateListDto);
        return R.ok(rankInviteList);
    }

    /**
     * 昨日排行领取商品接口
     */
    @PostMapping("/yesterday_obtain")
    public R<YesterdayObtainGoods> yesterdayObtain(@RequestBody @Valid YesterdayObtainGoodsDto rankAccumulateListDto){
        YesterdayObtainGoods yesterdayObtainGoods = glGoodsService.yesterdayObtain(rankAccumulateListDto);
        return R.ok(yesterdayObtainGoods);
    }

    /**
     * 累积排行领取商品接口
     */
    @PostMapping("/accumulate_obtain")
    public R<AccumulateObtainGoods> accumulateObtain(@RequestBody @Valid AccumulateObtainGoodsDto accumulateObtainGoodsDto){
        AccumulateObtainGoods accumulateObtainGoods = glGoodsService.accumulateObtain(accumulateObtainGoodsDto);
        return R.ok(accumulateObtainGoods);
    }

    @Operation(summary = "好友数排行接口", description = "好友数排行接口")
    @GetMapping("/friendsNumberRank")
    public R<List<FriendsNumberRankVo>> friendsNumberRank() {
        return success(glUserFriendsService.friendsNumberRank());
    }

    @Operation(summary = "今日之星排行接口", description = "今日之星排行接口")
    @GetMapping("/todayRank")
    public R<List<TodayRankVo>> todayRank() {
        return success(glUserFriendsService.todayRank());
    }

    @Operation(summary = "排行榜第一名接口", description = "排行榜第一名接口")
    @GetMapping("/firstRank")
    @PassToken
    public R<RankFirstVo> firstRank() {
        return success(glUserFriendsService.firstRank());
    }
}
