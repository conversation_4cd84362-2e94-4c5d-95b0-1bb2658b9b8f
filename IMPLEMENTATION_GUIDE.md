# 第二阶段检查功能实现指南

## 概述
本次实现了第二阶段检查功能，主要包括以下内容：

1. 在 `DataRecordType` 枚举中添加了 `PASS_FIRST_STAGE(3,"通过第一阶段")` 类型
2. 在 `DonationType` 枚举中添加了 `FUND(2, "基金")` 类型
3. 在 `GlBehaviorDao` 中添加了根据时间条件查询减排量的方法
4. 在 `DonationDao` 中添加了统计基金发放次数的方法
5. 在 `LowCarbonClockInServiceImpl` 中实现了完整的第二阶段检查和基金发放逻辑

## 核心业务逻辑

### 方法签名
```java
@Transactional(rollbackFor = Exception.class)
public void processSecondStageCheck(String mobileSha256)
```

### 进入第二阶段的条件（满足其一即可）
1. **减排量达标**：用户总减排量 >= 80KG
2. **活动结束**：当前时间 > 活动结束时间

### 基金发放规则
- **进入第二阶段后**，每当减排量增加超过10KG就发放一次基金
- **基准点**：基金发放的基准是**上次基金发放时的减排量**，如果没有发放记录则使用进入第二阶段时的减排量
- **参数变更友好**：即使基金间隔从10KG改为5KG，也不会影响之前的发放记录
- **补发机制**：基于当前参数计算应发放次数，一次性发放

### 业务流程

#### 1. 首次进入第二阶段判断
- 查询 `gl_data_record` 表检查是否已记录进入第二阶段
- 如果没有记录：
  - 获取用户当前总减排量
  - 判断是否满足进入条件（减排量 >= 80KG 或 活动已结束）
  - 如果满足：记录进入第二阶段（包含进入时的减排量）
  - 立即检查基金发放逻辑

#### 2. 已进入第二阶段的处理
- 从记录中解析进入第二阶段时的减排量
- 查询最新一次基金发放记录，获取上次发放时的减排量
- 确定基准减排量：如果有基金记录则使用上次发放时的减排量，否则使用进入时减排量
- 获取用户当前总减排量
- 计算应发放基金次数：`(当前减排量 - 基准减排量) / 当前间隔值`
- 如果应发放次数 >= 1，则批量发放基金

### 关键常量
```java
final BigDecimal FIRST_STAGE_EMISSION_THRESHOLD = new BigDecimal("80"); // 第一阶段减排量阈值（KG）
final BigDecimal FUND_EMISSION_INTERVAL = new BigDecimal("10"); // 基金发放间隔（KG）
final Date activityEndTime = new Date(); // TODO: 活动结束时间变量，后续需要从配置中获取
final BigDecimal FUND_AMOUNT_PER_TIME = new BigDecimal("100"); // TODO: 每次基金发放金额（临时值）
```

## 数据库操作

### 查询操作
1. 查询是否已进入第二阶段：`gl_data_record` 表，条件：`mobileSha256` 和 `type = 3`
2. 查询用户总减排量：`gl_behavior` 表，按 `mobileSha256` 汇总 `emission`
3. 查询活动结束后减排量：`gl_behavior` 表，条件：`mobileSha256` 和 `created >= activityEndTime`
4. 查询最新基金发放记录：`gl_donation` 表，条件：`mobileSha256` 和 `type = 2`，按 `id` 倒序

### 插入操作
1. 记录通过第一阶段：插入 `gl_data_record` 表，`dataJson` 存储进入时减排量
2. 批量发放基金：批量插入 `gl_donation` 表

## 数据存储格式

### gl_data_record 表
- **dataJson 字段**：`{"emissionAtEntry": "85.5"}`
  - 存储进入第二阶段时的减排量（千克）

### gl_donation 表
- **notes 字段**：`"95.8"`
  - 存储发放基金时的当前总减排量（千克）
- **type 字段**：`2` (DonationType.FUND)
- **amount 字段**：基金金额

## 核心算法

### 基金发放次数计算
```java
// 确定基准减排量
BigDecimal baseEmissionKg = entryEmissionKg; // 默认使用进入时减排量
Donation latestFundDonation = getLatestFundDonation(mobileSha256);
if (Objects.nonNull(latestFundDonation) && StrUtil.isNotBlank(latestFundDonation.getNotes())) {
    baseEmissionKg = new BigDecimal(latestFundDonation.getNotes()); // 使用上次发放时减排量
}

// 计算应发放次数
BigDecimal emissionDiff = currentEmissionKg.subtract(baseEmissionKg);
int shouldIssueTimes = emissionDiff.divide(FUND_EMISSION_INTERVAL, 0, RoundingMode.DOWN).intValue();
```

### 场景示例

#### 场景1：首次发放
- 用户A在80KG时进入第二阶段，当减排量达到105KG时
- 基准：80KG（进入时减排量）
- 差值：25KG，间隔10KG → 发放2次基金

#### 场景2：后续发放
- 用户A上次在90KG时发放过基金，当前减排量115KG
- 基准：90KG（上次发放时减排量）
- 差值：25KG，间隔10KG → 发放2次基金

#### 场景3：参数调整后
- 间隔从10KG改为5KG，用户B上次在100KG时发放过基金，当前107KG
- 基准：100KG（上次发放时减排量）
- 差值：7KG，间隔5KG → 发放1次基金

## 使用示例

```java
@Autowired
private LowCarbonClockInService lowCarbonClockInService;

// 调用第二阶段检查
public void checkUserSecondStage(String mobileSha256) {
    try {
        lowCarbonClockInService.processSecondStageCheck(mobileSha256);
        log.info("第二阶段检查完成，用户: {}", mobileSha256);
    } catch (Exception e) {
        log.error("第二阶段检查失败，用户: {}", mobileSha256, e);
    }
}
```

## 待完善的部分

### 1. 活动结束时间配置
当前使用的是 `new Date()`，需要从配置中获取实际的活动结束时间：
```java
// 建议从 gl_applet_config 表中获取配置
final Date activityEndTime = getActivityEndTimeFromConfig();
```

### 2. 基金金额计算规则
当前使用固定金额 `100`，需要实现具体的计算规则：
```java
// 建议根据减排量计算基金金额
final BigDecimal fundAmount = calculateFundAmount(currentEmissionKg);
```

## 测试建议

1. 测试首次进入第二阶段且减排量满足条件的场景
2. 测试首次进入第二阶段但减排量不足的场景
3. 测试活动结束后的基金发放逻辑
4. 测试已进入第二阶段的基金发放逻辑
5. 测试异常情况的处理

## 注意事项

1. 方法使用了 `@Transactional` 注解，确保数据一致性
2. 减排量单位转换：数据库存储的是克，业务逻辑使用千克
3. 基金发放记录的 `notes` 字段存储的是减排量数值的字符串形式
4. 所有数据库操作都有异常处理和日志记录
5. 使用了详细的日志记录便于问题排查

## 相关文件修改清单

1. `src/main/java/com/lvpuhui/gic/wxapp/common/enums/DataRecordType.java` - 添加枚举值
2. `src/main/java/com/lvpuhui/gic/wxapp/clock/enums/DonationType.java` - 添加枚举值
3. `src/main/java/com/lvpuhui/gic/wxapp/dao/GlBehaviorDao.java` - 添加查询方法
4. `src/main/java/com/lvpuhui/gic/wxapp/clock/dao/DonationDao.java` - 添加统计方法
5. `src/main/resources/mapper/clock/DonationDao.xml` - 添加SQL查询
6. `src/main/java/com/lvpuhui/gic/wxapp/clock/service/LowCarbonClockInService.java` - 添加接口方法
7. `src/main/java/com/lvpuhui/gic/wxapp/clock/service/impl/LowCarbonClockInServiceImpl.java` - 实现业务逻辑
8. `src/test/java/com/lvpuhui/gic/wxapp/clock/service/impl/LowCarbonClockInServiceImplTest.java` - 单元测试
9. `IMPLEMENTATION_GUIDE.md` - 实现指南文档

## 新增的方法

### LowCarbonClockInServiceImpl 私有方法
- `getEntryEmissionFromDataRecord(DataRecord)` - 解析进入第二阶段时的减排量
- `recordPassFirstStageWithEmission(String, BigDecimal)` - 记录进入第二阶段（含减排量）
- `getLatestFundDonation(String)` - 获取用户最新一次基金发放记录
- `checkAndIssueFunds(String, BigDecimal, BigDecimal, BigDecimal, BigDecimal)` - 检查并发放基金
- `issueFundMultipleTimes(String, int, BigDecimal, BigDecimal)` - 批量发放基金
