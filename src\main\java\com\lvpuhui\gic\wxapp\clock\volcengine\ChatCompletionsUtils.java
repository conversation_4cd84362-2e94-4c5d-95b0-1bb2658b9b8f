package com.lvpuhui.gic.wxapp.clock.volcengine;

import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.volcengine.ark.runtime.model.completion.chat.*;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 火山AI识图
 */
@Slf4j
public class ChatCompletionsUtils {

    static String apiKey = "efc46c6e-ec84-4c9c-9f95-009011954538";
    static String model = "ep-20250410101036-hr7wc";
    static String baseUrl = "https://ark.cn-beijing.volces.com/api/v3";
    static ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
    static Dispatcher dispatcher = new Dispatcher();
    static ArkService service = ArkService.builder()
            .dispatcher(dispatcher)
            .connectionPool(connectionPool)
            .baseUrl(baseUrl)
            .apiKey(apiKey)
            .retryTimes(2)
            .connectTimeout(Duration.ofSeconds(2))
            .timeout(Duration.ofSeconds(5))
            .build();

    /**
     * 获取AI的回答
     * @param prompt ai提示词，一定要让返回值为是或者否即可
     * @param imageUrl 图片地址
     * @return AI回复
     */
    public static String getChatCompletions(String prompt,String imageUrl) {
        List<ChatMessage> messages = new ArrayList<>();
        List<ChatCompletionContentPart> multiParts = new ArrayList<>();
        multiParts.add(ChatCompletionContentPart.builder().type("text").text(prompt).build());
        multiParts.add(ChatCompletionContentPart.builder().type("image_url")
                .imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(imageUrl)).build());

        ChatMessage userMessage = ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .multiContent(multiParts)
                .build();
        messages.add(userMessage);

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .build();

        try {
            ChatCompletionResult chatCompletionResult = service.createChatCompletion(chatCompletionRequest);
            if(Objects.isNull(chatCompletionResult)){
                throw new GicWxAppException("请您稍候重试");
            }
            ChatCompletionChoice chatCompletionChoice = chatCompletionResult.getChoices().get(0);
            return chatCompletionChoice.getMessage().getContent().toString();
        }catch (GicWxAppException e){
            throw e;
        }catch (Exception e){
            log.error("getChatCompletions error",e);
            throw new GicWxAppException("请您稍候重试");
        }
    }
}
