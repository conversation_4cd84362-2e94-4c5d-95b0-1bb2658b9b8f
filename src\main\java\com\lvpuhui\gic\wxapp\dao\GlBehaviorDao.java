package com.lvpuhui.gic.wxapp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.entity.GlBehavior;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 绿色行为记录表 (来源大数据增量)(GlBehavior)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-06 13:45:10
 */
public interface GlBehaviorDao extends BaseMapper<GlBehavior> {

    @Select("SELECT SUM(emission) FROM gl_behavior WHERE mobile_sha256 = #{mobileSha256}")
    BigDecimal getSumEmissionByMobileSha256(@Param("mobileSha256") String mobileSha256);
}