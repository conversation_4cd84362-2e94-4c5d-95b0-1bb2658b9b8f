package com.lvpuhui.gic.wxapp.sign.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionRewardDO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 任务奖励发放表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
public interface GlLowcarbonActionRewardDao extends BaseMapper<GlLowcarbonActionRewardDO> {

    GlLowcarbonActionRewardDO selectByUserId(@Param("mobieShare") String mobieShare);

    GlLowcarbonActionRewardDO selectByUserIdAndType(@Param("mobieShare") String mobile);

    GlLowcarbonActionRewardDO selectByUserIdAndfourteen(@Param("mobieShare")String mobieShare);

    GlLowcarbonActionRewardDO selectByUserIdAndTwenty(@Param("mobieShare")String mobieShare);
}
