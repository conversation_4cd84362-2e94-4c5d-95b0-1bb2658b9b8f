package com.lvpuhui.gic.wxapp.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 根据code获取openid和加密手机号Dto
 * <AUTHOR>
 * @since 2022年05月07日 16:43:00
 */
@Data
public class CodeOpenidDto {

    /**
     * 小程序登录code
     */
    @NotBlank(message = "参数错误")
    private String jsCode;

    /**
     * 小程序获取手机号code
     */
    @NotBlank(message = "参数错误")
    private String mobileCode;
}
