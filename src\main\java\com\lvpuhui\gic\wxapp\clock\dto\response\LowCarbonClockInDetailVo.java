package com.lvpuhui.gic.wxapp.clock.dto.response;

import com.lvpuhui.gic.wxapp.infrastructure.jackson.ImagePrefix;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Schema(description = "打卡详情返回值")
public class LowCarbonClockInDetailVo {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "打卡事项名称")
    private String name;

    @Schema(description = "打卡事项icon")
    @ImagePrefix
    private String icon;

    @Schema(description = "打卡提示信息")
    private String tipMessage;

    @Schema(description = "减排量(克)")
    private BigDecimal emission;

    @Schema(description = "间隔时间(小时)")
    private Integer gapTime;
}