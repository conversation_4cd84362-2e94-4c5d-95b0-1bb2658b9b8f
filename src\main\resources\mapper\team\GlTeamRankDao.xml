<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.team.dao.GlTeamRankDao">
    <update id="updateDeductEmission">
        UPDATE gl_team_rank SET emission = IF(emission - #{emission} &lt; 0,0,emission - #{emission}) WHERE team_id = #{teamId}
    </update>
    <update id="updateIncrementEmission">
        UPDATE gl_team_rank SET emission = IFNULL(emission,0) + #{emission} WHERE team_id = #{teamId}
    </update>

    <select id="selectTeamRanks" resultType="com.lvpuhui.gic.wxapp.team.dto.TeamRankListVo">
        SELECT
            gtr.team_id,
            gtr.`rank`,
            gtr.emission,
            gt.team_name,
            gt.team_logo,
            gtre.region_name
        FROM
            gl_team_rank gtr
                INNER JOIN gl_team gt ON gtr.team_id = gt.id
                LEFT JOIN gl_team_region gtre ON gt.region_id = gtre.id
        WHERE gt.`status` = 0
        ORDER BY -gtr.`rank` DESC
            LIMIT 20
    </select>
    <select id="selectTeamRankByTeamId" resultType="com.lvpuhui.gic.wxapp.team.dto.TeamRankListVo">
        SELECT
            gtr.team_id,
            IF(gt.`status` != 0,NULL,gtr.`rank`) AS `rank`,
            gtr.emission,
            gt.team_name,
            gt.team_logo,
            gtre.region_name
        FROM
            gl_team gt
                INNER JOIN gl_team_rank gtr ON gt.id = gtr.team_id
                LEFT JOIN gl_team_region gtre ON gt.region_id = gtre.id
        WHERE gt.id = #{teamId}
    </select>

    <select id="selectTeamRegionRanks" resultType="com.lvpuhui.gic.wxapp.team.dto.TeamRankRegionListVo">
        SELECT
            id AS teamId,
            emission,
            team_name,
            team_logo
        FROM
            gl_team
        WHERE
            region_code = #{regionCode}
        ORDER BY
            emission DESC
        LIMIT 20
    </select>

    <select id="selectTeamRankRegionByTeamId" resultType="com.lvpuhui.gic.wxapp.team.dto.TeamRankRegionListVo">
        SELECT
            gtr.team_id,
            IF(gt.`status` != 0,NULL,gtr.id_rank) AS `rank`,
            gtr.emission,
            gt.team_name,
            gt.team_logo
        FROM
            gl_team gt
                INNER JOIN gl_team_local_rank gtr ON gt.id = gtr.team_id
                LEFT JOIN gl_team_region gtre ON gt.region_id = gtre.id
        WHERE gt.id = #{teamId}
    </select>

    <select id="getTeamRankFirstUser" resultType="com.lvpuhui.gic.wxapp.entity.vo.RankFirstVo$RankFirstUserVo">
        SELECT
            gt.team_name AS nickName,
            gt.team_logo AS avatarUrl
        FROM
            gl_team_rank gtr
                INNER JOIN gl_team gt ON gtr.team_id = gt.id
                LEFT JOIN gl_team_region gtre ON gt.region_id = gtre.id
        WHERE
            gt.`status` = 0
        ORDER BY
            - gtr.`rank` DESC
        LIMIT 1
    </select>

    <select id="getAreaRankFirstUser" resultType="com.lvpuhui.gic.wxapp.entity.vo.RankFirstVo$RankFirstUserVo">
        SELECT
            gtr.region_name AS nickName
        FROM
            gl_team_region gtr
                INNER JOIN ( SELECT region_id, SUM( emission ) AS emission FROM gl_team GROUP BY region_id ) gt ON gtr.id = gt.region_id
        ORDER BY
            gt.emission DESC
        LIMIT 1
    </select>

    <select id="getAreaRankFirst" resultType="com.lvpuhui.gic.wxapp.entity.vo.TeamRankFirstVo">
        SELECT region_id, SUM( emission ) AS emission FROM gl_team GROUP BY region_id ORDER BY emission DESC LIMIT 1
    </select>
</mapper>

