package com.lvpuhui.gic.wxapp.infrastructure.exception;

import org.springframework.http.HttpStatus;

/**
 * 授权自定义异常
 * <AUTHOR>
 * @since 2023年06月19日 10:21:00
 */
public class GicWxAppAuthException extends RuntimeException{

    /**
     * 业务错误码
     */
    private long code;

    private static final long serialVersionUID = 2L;

    public GicWxAppAuthException(String msg) {
        super(msg);
        this.code = HttpStatus.UNAUTHORIZED.value();
    }

    public GicWxAppAuthException(long code, String msg) {
        super(msg);
        this.code = code;
    }

    public long getCode() {
        return code;
    }
}