package com.lvpuhui.gic.wxapp.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.infrastructure.interceptor.PassToken;
import com.lvpuhui.gic.wxapp.service.DataHandleService;
import com.lvpuhui.gic.wxapp.service.GlBehaviorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("")
@Slf4j
public class HandleController {

    @Resource
    GlBehaviorService glBehaviorService;
    @Resource
    DataHandleService dataHandleService;

    /**
     * 处理数据
     */
    @PassToken
    @PostMapping("/behavior")
    public R<String> processBehavior()  {
        glBehaviorService.processBehavior();
        return R.ok("");
    }

    @PassToken
    @PostMapping("/behavior1")
    public R<Integer> behavior(String created, Integer isHandlePoints)  {
        StopWatch sw = new StopWatch();
        sw.start();
        int k = glBehaviorService.handleBehaviorV2(created);
        sw.stop();
        log.info("执行绿色行为数据 耗时:{}",sw.getTotalTimeSeconds());
        if(isHandlePoints == null){
            isHandlePoints = 0;
        }
        if(isHandlePoints!=0){
            log.warn("isHandlePoints {} stop.",isHandlePoints);
            return R.ok(k);
        }
        ThreadUtil.execute(()->{
            glBehaviorService.handlePointsV2(StrUtil.isBlank(created)?
                    String.format("%s,%s",DateUtil.offsetDay(new Date(),-1)
                            .setField(Calendar.HOUR_OF_DAY,00)
                            .setField(Calendar.MINUTE,00)
                            .setField(Calendar.SECOND,00).toString(),DateUtil.offsetDay(new Date(),-1)
                            .setField(Calendar.HOUR_OF_DAY,23)
                            .setField(Calendar.MINUTE,59)
                            .setField(Calendar.SECOND,59).toString())
                    :created);
            // 执行webConsole的数据统计方法
            dataHandleService.callStatisticsMethod();
        });
        return R.ok(k);
    }

    @PassToken
    @PostMapping("/handlerPoints")
    public R<String> handlerPoints(String created)  {
        ThreadUtil.execute(()->{
            glBehaviorService.handlePointsV2(StrUtil.isBlank(created)?
                    String.format("%s,%s",DateUtil.offsetDay(new Date(),-1)
                            .setField(Calendar.HOUR_OF_DAY,00)
                            .setField(Calendar.MINUTE,00)
                            .setField(Calendar.SECOND,00).toString(),DateUtil.offsetDay(new Date(),-1)
                            .setField(Calendar.HOUR_OF_DAY,23)
                            .setField(Calendar.MINUTE,59)
                            .setField(Calendar.SECOND,59).toString())
                    :created);
            // 执行webConsole的数据统计方法
            dataHandleService.callStatisticsMethod();
        });
        return R.ok("ok");
    }

    @PostMapping("/calc_points")
    public R<String> calcPoints(String mobileSha256)  {
        glBehaviorService.calcPoints(mobileSha256);
        return R.ok("");
    }
}
