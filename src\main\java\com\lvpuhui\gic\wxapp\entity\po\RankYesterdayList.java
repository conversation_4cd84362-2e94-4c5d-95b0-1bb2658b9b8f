package com.lvpuhui.gic.wxapp.entity.po;

import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 昨日排行榜po
 * <AUTHOR>
 * @since 2022年07月25日 18:10:00
 */
@Data
public class RankYesterdayList {

    /**
     * 是否已过截止日期 true:已过  false:未过
     */
    private Boolean isEnd;

    /**
     * 领取商品名称
     */
    private String goodsName;

    /**
     * 需要上榜的天数（才可以领取商品）
     */
    private Integer days;

    /**
     * 当前用户信息
     */
    private YesterdayRankUser currentUser;

    /**
     * 昨天上榜用户信息集合
     */
    private List<YesterdayRankUser> yesterdayRankUsers;

    @Data
    public static class YesterdayRankUser{

        /**
         * 排名
         */
        private Long rank;

        /**
         * 减排量
         */
        private Double emission;

        /**
         * 昵称
         */
        private String nickName;

        /**
         * 头像
         */
        private String avatarUrl;

        /**
         * 用户id(sha256)
         */
        private String mobileSha256;

        /**
         * 上榜天数
         */
        private Integer rankDays;

        public Double getEmission() {
            if(Objects.isNull(emission) || emission < 0){
                return 0D;
            }
            return emission;
        }
    }
}
