package com.lvpuhui.gic.wxapp.sign.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 低碳小事表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_lowcarbon_action")
public class GlLowcarbonActionDO extends Model<GlLowcarbonActionDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 分类id
     */
    private Integer cateId;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String description;

    /**
     * 点亮的icon图标地址
     */
    private String iconLight;

    /**
     * 暗色图标
     */
    private String iconDark;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否展示0否1是
     */
    private Integer isShow;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 软删除
     */
    private LocalDateTime deletedAt;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
