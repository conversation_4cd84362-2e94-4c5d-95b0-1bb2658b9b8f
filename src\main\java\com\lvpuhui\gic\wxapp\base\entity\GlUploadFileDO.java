package com.lvpuhui.gic.wxapp.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 上传文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_upload_file")
public class GlUploadFileDO extends Model<GlUploadFileDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 字节数
     */
    private Integer fileSize;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     *  删除 0未删除 1已删除
     */
    private Integer deleted;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
