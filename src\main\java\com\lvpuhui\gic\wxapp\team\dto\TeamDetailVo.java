package com.lvpuhui.gic.wxapp.team.dto;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 我的战队详情
 * <AUTHOR>
 * @since 2023年05月24日 15:51:00
 */
@Data
public class TeamDetailVo {

    /**
     * 是否为负责人 true:是负责人  false:不是负责人
     */
    private Boolean isCaptain = false;

    /**
     * 战队ID
     */
    private Long id;

    /**
     * 战队名称
     */
    private String teamName;

    /**
     * 战队Logo
     */
    private String teamLogo;

    /**
     * 战队LOGO ID
     */
    private String teamLogoId;

    /**
     * 加入方式,无限制:0,邀请:1
     */
    private Integer joinMode;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 成员人数
     */
    private Integer memberCount;

    /**
     * 减排量
     */
    private BigDecimal emission;

    /**
     * 减排量-文本
     */
    private String emissionText;

    /**
     * 创建人昵称
     */
    private String nickName;

    /**
     * 创建人头像
     */
    private String avatarUrl;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd")
    private LocalDateTime created;

    /**
     * 有减排量的人数
     */
    private Integer emissionUserNumber;

    /**
     * 人均贡献减排量
     */
    private String avgEmission;

    /**
     * 区域code
     */
    private String regionCode;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 减排贡献榜-成员集合
     */
    private List<TeamDetailMemberVo> memberVos;

    /**
     * 当前登录人的减排量
     */
    private String myEmissionText;

    @Schema(description = "战队排名")
    private Long rank;

    @Schema(description = "当前登录人队内排名")
    private Long memberRank;

    public String getEmissionText() {
        if(Objects.nonNull(emission)){
            return CalcUtil.weightFormat(emission.doubleValue());
        }
        return "0g";
    }

    public String getMyEmissionText() {
        return StrUtil.blankToDefault(myEmissionText,"0g");
    }

    public String getAvgEmission() {
        return StrUtil.blankToDefault(avgEmission,"0g");
    }
}
