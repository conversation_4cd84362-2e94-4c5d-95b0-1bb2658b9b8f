package com.lvpuhui.gic.wxapp.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SaveServiceMessageDto {
    @NotNull(message = "请您选择模板")
    private String templateId;
    @NotNull(message = "请您选择模板")
    private String messageName;
    @NotNull(message = "请您输入跳转连接")
    private String jumpUrl;
}
