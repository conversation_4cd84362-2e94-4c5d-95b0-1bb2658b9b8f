package com.lvpuhui.gic.wxapp.clock.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 低碳打卡明细表
 */
@Schema(description="低碳打卡明细表")
@Getter
@Setter
@TableName(value = "gl_low_carbon_clock_in_details")
public class LowCarbonClockInDetails {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键")
    @NotNull(message = "主键不能为null")
    private Long id;

    /**
     * 手机号sha256
     */
    @TableField(value = "mobile_sha256")
    @Schema(description="手机号sha256")
    @Size(max = 100,message = "手机号sha256最大长度要小于 100")
    private String mobileSha256;

    /**
     * 低碳打卡ID
     */
    @TableField(value = "clock_in_id")
    @Schema(description="低碳打卡ID")
    private Long clockInId;

    /**
     * 减排量(克)
     */
    @TableField(value = "emission")
    @Schema(description="减排量(克)")
    private BigDecimal emission;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description="描述")
    @Size(max = 100,message = "描述最大长度要小于 100")
    private String description;

    /**
     * 行为名称
     */
    @TableField(value = "behavior_name")
    @Schema(description="行为名称")
    @Size(max = 100,message = "行为名称最大长度要小于 100")
    private String behaviorName;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description="创建时间")
    private Date created;

    @TableField(value = "source")
    @Schema(description="来源 0 来源拍照打卡  1来源行为")
    private Integer source;
}