<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.clock.dao.DonationDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.clock.entity.Donation">
    <!--@mbg.generated-->
    <!--@Table gl_donation-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mobile_sha256" jdbcType="VARCHAR" property="mobileSha256" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, mobile_sha256, `type`, amount, created,notes
  </sql>

  <select id="getSumAmount" resultType="java.math.BigDecimal">
    SELECT sum(amount) FROM gl_donation WHERE mobile_sha256 = #{mobileSha256} AND type = #{type}
  </select>

  <select id="getDonationGroup" resultType="com.lvpuhui.gic.wxapp.clock.dto.DonationGroupDto">
    SELECT type, sum(amount) as sumAmount FROM gl_donation WHERE mobile_sha256 = #{mobileSha256} GROUP BY type
  </select>

  <select id="getSumAmountAll" resultType="java.math.BigDecimal">
    SELECT sum(amount) FROM gl_donation
  </select>
</mapper>