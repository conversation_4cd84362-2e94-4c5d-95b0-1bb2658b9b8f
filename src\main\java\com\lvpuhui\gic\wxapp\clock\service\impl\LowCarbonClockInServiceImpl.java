package com.lvpuhui.gic.wxapp.clock.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lvpuhui.gic.wxapp.clock.dao.DonationDao;
import com.lvpuhui.gic.wxapp.clock.dao.LowCarbonClockInDao;
import com.lvpuhui.gic.wxapp.clock.dao.LowCarbonClockInDetailsDao;
import com.lvpuhui.gic.wxapp.clock.dto.DonationGroupDto;
import com.lvpuhui.gic.wxapp.clock.dto.PointsObtainDto;
import com.lvpuhui.gic.wxapp.clock.dto.UserClockStatDto;
import com.lvpuhui.gic.wxapp.clock.dto.UserStatDto;
import com.lvpuhui.gic.wxapp.clock.dto.request.ClockInDto;
import com.lvpuhui.gic.wxapp.clock.dto.response.*;
import com.lvpuhui.gic.wxapp.clock.entity.Donation;
import com.lvpuhui.gic.wxapp.clock.entity.LowCarbonClockIn;
import com.lvpuhui.gic.wxapp.clock.entity.LowCarbonClockInDetails;
import com.lvpuhui.gic.wxapp.clock.enums.DonationType;
import com.lvpuhui.gic.wxapp.clock.enums.Whether;
import com.lvpuhui.gic.wxapp.clock.service.LowCarbonClockInService;
import com.lvpuhui.gic.wxapp.clock.volcengine.ChatCompletionsUtils;
import com.lvpuhui.gic.wxapp.common.dao.DataRecordDao;
import com.lvpuhui.gic.wxapp.common.dao.ShareDao;
import com.lvpuhui.gic.wxapp.common.entity.DataRecord;
import com.lvpuhui.gic.wxapp.common.entity.Share;
import com.lvpuhui.gic.wxapp.common.enums.DataRecordType;
import com.lvpuhui.gic.wxapp.common.enums.ShareType;
import com.lvpuhui.gic.wxapp.dao.*;
import com.lvpuhui.gic.wxapp.entity.*;
import com.lvpuhui.gic.wxapp.entity.vo.CertificateVo;
import com.lvpuhui.gic.wxapp.enums.BehaviorType;
import com.lvpuhui.gic.wxapp.enums.MedalUserState;
import com.lvpuhui.gic.wxapp.enums.PointsType;
import com.lvpuhui.gic.wxapp.friends.dao.FriendsRankFabDao;
import com.lvpuhui.gic.wxapp.infrastructure.constant.AppletConfigConstant;
import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Available;
import com.lvpuhui.gic.wxapp.infrastructure.enums.Deleted;
import com.lvpuhui.gic.wxapp.infrastructure.enums.PublishState;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicClockInWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import com.lvpuhui.gic.wxapp.infrastructure.utils.SpringContextUtils;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.medal.dao.GlMedalUserDao;
import com.lvpuhui.gic.wxapp.medal.dao.MedalClockDetailDao;
import com.lvpuhui.gic.wxapp.medal.dao.MedalDao;
import com.lvpuhui.gic.wxapp.medal.entity.GlMedalUser;
import com.lvpuhui.gic.wxapp.medal.entity.Medal;
import com.lvpuhui.gic.wxapp.medal.entity.MedalClockDetail;
import com.lvpuhui.gic.wxapp.service.GlPointsService;
import com.lvpuhui.gic.wxapp.team.dao.GlTeamMemberDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LowCarbonClockInServiceImpl implements LowCarbonClockInService {

    @Resource
    private LowCarbonClockInDao lowCarbonClockInDao;

    @Resource
    private GlUserDao glUserDao;

    @Resource
    private GlAppletConfigDao glAppletConfigDao;

    @Resource
    private LowCarbonClockInDetailsDao lowCarbonClockInDetailsDao;

    @Resource
    private GlBehaviorDao glBehaviorDao;

    @Resource
    private GlTickoffDao glTickoffDao;

    @Resource
    private GlTeamMemberDao glTeamMemberDao;

    @Resource
    private GlUserFriendsDao glUserFriendsDao;

    @Resource
    private FriendsRankFabDao friendsRankFabDao;

    @Resource
    private GlMedalUserDao glMedalUserDao;

    @Resource
    private MedalDao medalDao;

    @Resource
    private MedalClockDetailDao medalClockDetailDao;

    @Resource
    private GlPointsService glPointsService;

    @Resource
    private ShareDao shareDao;

    @Resource
    private TodayEmissionDao todayEmissionDao;

    @Resource
    private GlRankDao glRankDao;

    @Resource
    private DonationDao donationDao;

    @Resource
    private DataRecordDao dataRecordDao;

    private final Map<String, String> concurrency = Maps.newConcurrentMap();

    private final Map<String, Object> shareActivityProgressMap = Maps.newConcurrentMap();

    private List<Medal> MEDALS = Lists.newArrayList();

    ThreadPoolExecutor executorService = new ThreadPoolExecutor(
            1, // corePoolSize
            6, // maximumPoolSize
            0L, TimeUnit.MILLISECONDS, // keepAliveTime, unit
            new LinkedBlockingQueue<>(500), Executors.defaultThreadFactory(),new ThreadPoolExecutor.CallerRunsPolicy());

    @PostConstruct
    public void initMedals() {
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                log.info("initMedals");
                LambdaQueryWrapper<Medal> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(Medal::getDeleted, Deleted.UN_DELETE.getDeleted());
                queryWrapper.eq(Medal::getState, PublishState.PUBLISH.getState());
                queryWrapper.ne(Medal::getId, -1);
                List<Medal> medals = medalDao.selectList(queryWrapper);
                if(CollUtil.isEmpty(medals)){
                    return;
                }
                MEDALS = medals;
            }
        };
        Timer timer = new Timer();
        timer.schedule(timerTask, 1000, 10 * 60 * 1000);
    }

    @Override
    public void resetMedals() {
        log.info("resetMedals");
        LambdaQueryWrapper<Medal> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Medal::getDeleted, Deleted.UN_DELETE.getDeleted());
        queryWrapper.eq(Medal::getState, PublishState.PUBLISH.getState());
        List<Medal> medals = medalDao.selectList(queryWrapper);
        if(CollUtil.isEmpty(medals)){
            return;
        }
        MEDALS = medals;
    }

    @Override
    public List<LowCarbonClockInListVo> list() {
        // 根据sequence字段排序，越大排越前，并且判断deleted是否为0
        LambdaQueryWrapper<LowCarbonClockIn> lowCarbonClockInLambdaQueryWrapper = Wrappers.lambdaQuery();
        lowCarbonClockInLambdaQueryWrapper.eq(LowCarbonClockIn::getState, PublishState.PUBLISH.getState());
        lowCarbonClockInLambdaQueryWrapper.eq(LowCarbonClockIn::getDeleted, Deleted.UN_DELETE.getDeleted())
                .orderByDesc(LowCarbonClockIn::getSequence);
        List<LowCarbonClockIn> lowCarbonClockInList = lowCarbonClockInDao.selectList(lowCarbonClockInLambdaQueryWrapper);
        if(CollUtil.isEmpty(lowCarbonClockInList)){
            return Lists.newArrayList();
        }
        return lowCarbonClockInList.stream()
                        .map(lowCarbonClockIn -> {
                            LowCarbonClockInListVo lowCarbonClockInListVo = new LowCarbonClockInListVo();
                            BeanUtils.copyProperties(lowCarbonClockIn, lowCarbonClockInListVo);
                            return lowCarbonClockInListVo;
                        }).collect(Collectors.toList());
    }

    @Override
    public List<LowCarbonClockInDetailListVo> detail(Integer page) {
        page = Objects.isNull(page) || page <= 0 ? 1 : page;

        String mobileSha256 = UserUtils.getMobileSha256();

        LambdaQueryWrapper<LowCarbonClockInDetails> clockInDetailsLambdaQueryWrapper = Wrappers.lambdaQuery();
        clockInDetailsLambdaQueryWrapper.eq(LowCarbonClockInDetails::getMobileSha256, mobileSha256);
        clockInDetailsLambdaQueryWrapper.orderByDesc(LowCarbonClockInDetails::getCreated);

        Page<LowCarbonClockInDetails> page1 = new Page<>(page, 10);
        Page<LowCarbonClockInDetails> lowCarbonClockInDetailsPage = lowCarbonClockInDetailsDao.selectPage(page1, clockInDetailsLambdaQueryWrapper);
        if(Objects.nonNull(lowCarbonClockInDetailsPage) && CollUtil.isNotEmpty(lowCarbonClockInDetailsPage.getRecords())){
            return lowCarbonClockInDetailsPage.getRecords()
                    .stream()
                    .map(lowCarbonClockInDetails -> {
                        LowCarbonClockInDetailListVo lowCarbonClockInDetailListVo = new LowCarbonClockInDetailListVo();
                        BeanUtils.copyProperties(lowCarbonClockInDetails, lowCarbonClockInDetailListVo);
                        return lowCarbonClockInDetailListVo;
                    }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public UserInfoVo userInfo(String mobileSha256) {
        GlUser glUser;
        if(StrUtil.isNotBlank(mobileSha256)){
            LambdaQueryWrapper<GlUser> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(GlUser::getMobileSha256, mobileSha256);
            glUser = glUserDao.selectOne(queryWrapper);
        }else {
            glUser = glUserDao.selectById(UserUtils.getUserId());
        }
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        GlAppletConfig glAppletConfig = glAppletConfigDao.selectByKey(AppletConfigConstant.POINTS_CONFIG);
        PointsObtainDto pointsObtainDto = JSON.parseObject(glAppletConfig.getParamValue(), PointsObtainDto.class);
        BigDecimal activitySumEmission = pointsObtainDto.getCarbonEmissionLimit();

        UserInfoVo userInfoVo = new UserInfoVo();
        userInfoVo.setTime(LocalDateTime.now());
        userInfoVo.setAvatarUrl(glUser.getAvatarUrl());
        userInfoVo.setNickName(glUser.getNickName());
        userInfoVo.setUserNumber(StrUtil.padPre(glUser.getId().toString(), 5, "0"));
        userInfoVo.setPoints(glUser.getPointRemain());
        BigDecimal emission = glBehaviorDao.getSumEmissionByMobileSha256(glUser.getMobileSha256());
        emission = Objects.isNull(emission)?BigDecimal.ZERO:emission;
        userInfoVo.setEmission(emission);
        List<Long> obtainMedalIds = glMedalUserDao.getObtainMedalIds(glUser.getMobileSha256());
        userInfoVo.setMedalNumber(obtainMedalIds.size());
        userInfoVo.setEmissionFormat(CalcUtil.weightFormatEnglish(userInfoVo.getEmission().doubleValue()));
        // 计算减排量占比
        BigDecimal radio = userInfoVo.getEmission()
                .divide(activitySumEmission, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        userInfoVo.setRadio(radio);

        // 查询配捐
        List<DonationGroupDto> donationGroupDtoList = donationDao.getDonationGroup(glUser.getMobileSha256());
        if(CollUtil.isNotEmpty(donationGroupDtoList)){
            // 根据type转map
            Map<Integer, DonationGroupDto> donationGroupDtoMap = donationGroupDtoList.stream().collect(Collectors.toMap(DonationGroupDto::getType, Function.identity(), (k1, k2) -> k1));
            if(MapUtil.isNotEmpty(donationGroupDtoMap)){
                if(donationGroupDtoMap.containsKey(DonationType.REDUCTION.getCode())){
                    DonationGroupDto donationGroupDto = donationGroupDtoMap.get(DonationType.REDUCTION.getCode());
                    if(Objects.nonNull(donationGroupDto.getSumAmount()) && donationGroupDto.getSumAmount().compareTo(BigDecimal.ZERO) > 0){
                        BigDecimal divide = donationGroupDto.getSumAmount().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                        userInfoVo.setDonationEmissionAmount(divide);
                    }
                }
                if(donationGroupDtoMap.containsKey(DonationType.CONTRIBUTION_POINTS.getCode())){
                    DonationGroupDto donationGroupDto = donationGroupDtoMap.get(DonationType.CONTRIBUTION_POINTS.getCode());
                    if(Objects.nonNull(donationGroupDto.getSumAmount()) && donationGroupDto.getSumAmount().compareTo(BigDecimal.ZERO) > 0){
                        BigDecimal divide = donationGroupDto.getSumAmount().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                        userInfoVo.setDonationPointsAmount(divide);
                    }
                }
            }
        }
        LambdaQueryWrapper<DataRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(DataRecord::getMobileSha256, glUser.getMobileSha256());
        queryWrapper.eq(DataRecord::getType, DataRecordType.GUARD.getType());
        queryWrapper.orderByDesc(DataRecord::getId);
        queryWrapper.last("limit 1");
        DataRecord dataRecord = dataRecordDao.selectOne(queryWrapper);
        if(Objects.nonNull(dataRecord)){
            String dataJson = dataRecord.getDataJson();
            JSONObject jsonObject = JSON.parseObject(dataJson);
            userInfoVo.setGuardId(jsonObject.getLong("id"));
        }

        // 查询配捐所有用户的总额
        BigDecimal sumAmount = donationDao.getSumAmountAll();
        if(Objects.nonNull(sumAmount)){
            BigDecimal totalAmount = sumAmount.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            userInfoVo.setDonationTotalAmount(totalAmount);
        }
        return userInfoVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal clockIn(ClockInDto clockInDto) {
        String fileUrl = clockInDto.getFileUrl();
        Long clockInId = clockInDto.getClockInId();
        String mobileSha256 = UserUtils.getMobileSha256();
        LowCarbonClockInService bean = SpringContextUtils.getBean(LowCarbonClockInService.class);
        if(concurrency.containsKey(mobileSha256)){
            throw new GicClockInWxAppException("您正在打卡，勿重复点击");
        }
        BigDecimal amount = BigDecimal.ZERO;
        try {
            concurrency.put(mobileSha256, "true");
            GlAppletConfig clockInConfig = glAppletConfigDao.selectByKey(AppletConfigConstant.CLOCK_IN_DAY_MAX);
            int clockInDayMax = Integer.parseInt(clockInConfig.getParamValue());
            // 判断地址是否为外网地址
            LambdaQueryWrapper<GlAppletConfig> glAppletConfigLambdaQueryWrapper = Wrappers.lambdaQuery();
            glAppletConfigLambdaQueryWrapper.eq(GlAppletConfig::getParamKey, Global.IMAGE_PREFIX);
            GlAppletConfig glAppletConfig = glAppletConfigDao.selectOne(glAppletConfigLambdaQueryWrapper);
            String imagePrefix = glAppletConfig.getParamValue();
            if(!fileUrl.startsWith(imagePrefix)){
                throw new GicWxAppException("图片地址错误");
            }
            LowCarbonClockIn lowCarbonClockIn = lowCarbonClockInDao.selectById(clockInId);
            if(Objects.isNull(lowCarbonClockIn)){
                throw new GicClockInWxAppException("请从正确入口打卡");
            }
            if(!PublishState.PUBLISH.getState().equals(lowCarbonClockIn.getState())){
                throw new GicClockInWxAppException("该打卡已下线");
            }
            if(Objects.equals(lowCarbonClockIn.getDeleted(), Deleted.DELETED.getDeleted())){
                throw new GicClockInWxAppException("请从正确入口打卡");
            }
            // 判断间隔时间
            LambdaQueryWrapper<LowCarbonClockInDetails> lastQueryWrapper = Wrappers.lambdaQuery();
            lastQueryWrapper.select(LowCarbonClockInDetails::getCreated);
            lastQueryWrapper.eq(LowCarbonClockInDetails::getMobileSha256, mobileSha256);
            lastQueryWrapper.eq(LowCarbonClockInDetails::getClockInId, clockInId);
            lastQueryWrapper.last("limit 1");
            lastQueryWrapper.orderByDesc(LowCarbonClockInDetails::getId);
            LowCarbonClockInDetails lastLowCarbonClockInDetails = lowCarbonClockInDetailsDao.selectOne(lastQueryWrapper);
            if(Objects.nonNull(lastLowCarbonClockInDetails) && DateUtil.between(lastLowCarbonClockInDetails.getCreated(), new Date(), DateUnit.HOUR) < lowCarbonClockIn.getGapTime()){
                throw new GicClockInWxAppException("该打卡间隔时间小于" + lowCarbonClockIn.getGapTime() + "小时");
            }
            String version = DateUtil.format(new Date(),DatePattern.PURE_DATE_PATTERN);
            int count = dataRecordDao.getCountByMobileSha256(mobileSha256, DataRecordType.CLOCK_IN.getType(),version);
            if(count > clockInDayMax){
                throw new GicClockInWxAppException("您今天打卡次数已达上限");
            }
            bean.saveDataRecord(mobileSha256,clockInDto,version);
            String prompt = lowCarbonClockIn.getAiTip();
            String result = ChatCompletionsUtils.getChatCompletions(prompt, fileUrl);
            if(StrUtil.isBlank(result)){
                throw new GicClockInWxAppException("打卡识别失败，请稍候重试");
            }
            if(Whether.NO.getDescribe().equals(result)){
                throw new GicClockInWxAppException("打卡识别不符合");
            }
            // 打卡成功
            LowCarbonClockInDetails lowCarbonClockInDetails = new LowCarbonClockInDetails();
            lowCarbonClockInDetails.setMobileSha256(mobileSha256);
            lowCarbonClockInDetails.setClockInId(clockInId);
            lowCarbonClockInDetails.setEmission(lowCarbonClockIn.getEmission());
            lowCarbonClockInDetails.setDescription(lowCarbonClockIn.getName());
            lowCarbonClockInDetails.setBehaviorName("");
            lowCarbonClockInDetails.setCreated(new Date());
            lowCarbonClockInDetailsDao.insert(lowCarbonClockInDetails);
            // 增加减排量
            GlBehavior glBehavior = new GlBehavior();
            glBehavior.setEventId(mobileSha256 + lowCarbonClockInDetails.getId());
            glBehavior.setMobileSha256(mobileSha256);
            glBehavior.setDate(new Date());
            glBehavior.setEmission(lowCarbonClockIn.getEmission().doubleValue());
            glBehavior.setCreated(new Date());
            glBehavior.setType(BehaviorType.CLOCk_IN.getState());
            glBehaviorDao.insert(glBehavior);

            // 判断增加配捐
            amount = bean.handlerDonation(mobileSha256, DonationType.REDUCTION.getCode(), lowCarbonClockIn.getEmission());
            // 增加今日减排量
            TodayEmission todayEmission = new TodayEmission();
            todayEmission.setMobileSha256(mobileSha256);
            todayEmission.setEmission(lowCarbonClockIn.getEmission());
            todayEmission.setVersion(DateUtil.format(new Date(),DatePattern.PURE_DATE_PATTERN));
            todayEmissionDao.insertOnUpdate(todayEmission);

            bean.processEmissionBasedFund(mobileSha256, lowCarbonClockIn.getEmission(), lowCarbonClockInDetails.getId());
        }catch (GicClockInWxAppException e){
            throw e;
        }catch (Exception e){
            log.error("clockIn error",e);
            throw new GicClockInWxAppException("打卡失败，请您稍候重试");
        }finally {
            concurrency.remove(mobileSha256);
        }
        bean.medalHandler(mobileSha256,UserUtils.getUserId());
        amount = Objects.isNull(amount)?BigDecimal.ZERO:amount.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        return amount;
    }

    @Async
    @Override
    public void saveDataRecord(String mobileSha256, ClockInDto clockInDto, String version){
        DataRecord dataRecord = new DataRecord();
        dataRecord.setMobileSha256(mobileSha256);
        dataRecord.setDataJson(JSON.toJSONString(clockInDto));
        dataRecord.setType(DataRecordType.CLOCK_IN.getType());
        dataRecord.setVersion(version);
        dataRecordDao.insert(dataRecord);
    }

    /**
     * 配捐处理
     * @param mobileSha256 手机号sha256
     * @param type 类型
     * @param value 减排量/助力值(积分)
     */
    @Override
    public BigDecimal handlerDonation(String mobileSha256, int type,BigDecimal value){
        GlAppletConfig glAppletConfig = glAppletConfigDao.selectByKey(AppletConfigConstant.POINTS_CONFIG);
        PointsObtainDto pointsObtainDto = JSON.parseObject(glAppletConfig.getParamValue(), PointsObtainDto.class);
        // 1.根据mobileSha256和类型查询配捐表总金额
        BigDecimal sumAmount = donationDao.getSumAmount(mobileSha256, type);
        if(Objects.isNull(sumAmount)){
            sumAmount = BigDecimal.ZERO;
        }
        if(DonationType.REDUCTION.getCode() == type){
            BigDecimal donationEmissionMax = pointsObtainDto.getDonationEmissionMax().multiply(BigDecimal.valueOf(100));
            if(sumAmount.compareTo(donationEmissionMax) >= 0){
                return BigDecimal.ZERO;
            }
            // 计算还可以最多配捐金额
            BigDecimal max = donationEmissionMax.subtract(sumAmount);
            // 计算减排量换算成配捐
            BigDecimal reduction = value.divide(pointsObtainDto.getDonationEmission(), 2, RoundingMode.HALF_UP);
            BigDecimal amount = reduction.compareTo(max) >= 0?max:reduction;
            Donation donation = new Donation();
            donation.setMobileSha256(mobileSha256);
            donation.setType(type);
            donation.setAmount(amount);
            donationDao.insert(donation);
            return amount;
        }
        BigDecimal donationPointsMax = pointsObtainDto.getDonationPointsMax().multiply(BigDecimal.valueOf(100));
        if(sumAmount.compareTo(donationPointsMax) >= 0){
            return BigDecimal.ZERO;
        }
        BigDecimal max = donationPointsMax.subtract(sumAmount);
        BigDecimal points = value.divide(java.math.BigDecimal.valueOf(pointsObtainDto.getDonationPoints()), 2, RoundingMode.HALF_UP);
        BigDecimal amount = points.compareTo(max) >= 0?max:points;
        Donation donation = new Donation();
        donation.setMobileSha256(mobileSha256);
        donation.setType(type);
        donation.setAmount(amount);
        donationDao.insert(donation);
        return amount;
    }

    @Async
    @Override
    public void medalHandler(String mobileSha256,Long userId){
        // 如果勋章列表为空，直接返回
        if (CollUtil.isEmpty(MEDALS)) {
            log.info("勋章列表为空，直接返回");
            return;
        }
        // 获取用户已经获得的勋章ID列表
        List<Long> obtainedMedalIds = glMedalUserDao.getObtainMedalIds(mobileSha256);
        // 过滤出用户尚未获得的勋章
        List<Medal> candidateMedals = filterCandidateMedals(MEDALS, obtainedMedalIds);
        // 如果没有候选勋章，直接返回
        if (candidateMedals.isEmpty()) {
            log.info("用户已经获得所有勋章:{}", mobileSha256);
            return;
        }
        // 获取需要判断打卡的ID集合
        List<Long> clockInIds = candidateMedals.stream()
                .filter(medal -> medal.getClockIn() > 0)
                .map(Medal::getId)
                .collect(Collectors.toList());

        Map<Long,Map<Long,Integer>> medalIdClockInCount = Maps.newHashMap();
        // 查询勋章打卡需要的次数
        if(CollUtil.isNotEmpty(clockInIds)){
            LambdaQueryWrapper<MedalClockDetail> medalClockDetailWrapper = Wrappers.lambdaQuery();
            medalClockDetailWrapper.in(MedalClockDetail::getMedalId, clockInIds);
            List<MedalClockDetail> medalClockDetails = medalClockDetailDao.selectList(medalClockDetailWrapper);
            Map<Long, List<MedalClockDetail>> medalClockDetailMap = medalClockDetails.stream()
                    .collect(Collectors.groupingBy(MedalClockDetail::getMedalId));
            for (Map.Entry<Long, List<MedalClockDetail>> entry : medalClockDetailMap.entrySet()) {
                Long medalId = entry.getKey();
                List<MedalClockDetail> medalClockDetailList = entry.getValue();
                Map<Long, Integer> map = medalClockDetailList.stream()
                        .collect(Collectors.toMap(MedalClockDetail::getClockInId, MedalClockDetail::getClockInNumber));
                medalIdClockInCount.put(medalId, map);
            }
        }
        // 收集用户的统计数据
        UserStatDto userStatDto = gatherUserStatistics(mobileSha256, userId, candidateMedals);
        // 遍历每个候选勋章，判断用户是否符合条件并授予勋章
        boolean isAwarded = false;
        boolean isPoints = false;
        for (Medal medal : candidateMedals) {
            if (isMedalEligible(userStatDto, medal,medalIdClockInCount)) {
                isAwarded = awardMedal(mobileSha256, medal);
                if (Objects.nonNull(medal.getGrantPoints()) && medal.getGrantPoints() > 0) {
                    isPoints = true;
                }
            }
        }
        if (isAwarded && isPoints) {
            LowCarbonClockInService bean = SpringContextUtils.getBean(LowCarbonClockInService.class);
            bean.medalHandler(mobileSha256,userId);
        }
    }

    /**
     * 过滤出用户尚未获得的勋章
     * @param medals 所有可用的勋章列表
     * @param obtainedMedalIds 用户已经获得的勋章ID列表
     * @return 用户尚未获得的勋章列表
     */
    private List<Medal> filterCandidateMedals(List<Medal> medals, List<Long> obtainedMedalIds) {
        return medals.stream()
                .filter(medal -> !obtainedMedalIds.contains(medal.getId()))
                .collect(Collectors.toList());
    }

    /**
     * 并发收集用户的统计数据
     * @param mobileSha256 手机号sha256
     * @param userId 用户ID
     * @param candidateMedals 候选勋章列表
     * @return 包含用户统计数据的 UserStatDto 对象
     */
    private UserStatDto gatherUserStatistics(String mobileSha256, Long userId, List<Medal> candidateMedals) {
        UserStatDto userStatDto = new UserStatDto();
        List<CompletableFuture<?>> futureList = new ArrayList<>();

        boolean isClockIn = false;
        boolean isEmission = false;
        boolean isPoints = false;
        boolean isShare = false;
        boolean isTickOff = false;
        boolean isTeam = false;
        boolean isFriend = false;
        boolean isLike = false;
        boolean isLiked = false;
        boolean isClockInSum = false;
        boolean isInvite = false;
        for (Medal medal : candidateMedals) {
            if(medal.getClockIn() > 0 && !isClockIn){
                futureList.add(supplyAsync(() -> lowCarbonClockInDetailsDao.getClockInCountGroup(mobileSha256)
                                .stream()
                                .collect(Collectors.toMap(UserClockStatDto::getClockInId, UserClockStatDto::getClockInCount)),
                        userStatDto::setClockInCount));
                isClockIn = true;
            }
            if(Objects.nonNull(medal.getEmission()) && !isEmission){
                futureList.add(supplyAsync(() -> glBehaviorDao.getSumEmissionByMobileSha256(mobileSha256), userStatDto::setEmission));
            }
            if(Objects.nonNull(medal.getCarbonPoints()) && !isPoints){
                futureList.add(supplyAsync(() -> {
                    LambdaQueryWrapper<GlUser> wrapper = Wrappers.lambdaQuery();
                    wrapper.select(GlUser::getPointRemain).eq(GlUser::getMobileSha256, mobileSha256);
                    GlUser glUser = glUserDao.selectOne(wrapper);
                    return glUser.getPointRemain();
                }, userStatDto::setCarbonScore));
            }
            if(Objects.nonNull(medal.getShareNumber()) && !isShare){
                futureList.add(supplyAsync(() -> shareDao.getCountByMobileSha256(mobileSha256), userStatDto::setShareCount));
            }
            if(Objects.nonNull(medal.getTickoffNumber()) && !isTickOff){
                futureList.add(supplyAsync(() -> glTickoffDao.getCountByMobileSha256(mobileSha256), userStatDto::setTickoffCount));
            }
            if(medal.getNeedTeam() > Available.NO.getState() && !isTeam){
                futureList.add(supplyAsync(() -> glTeamMemberDao.getExistByUserId(userId) > 0, userStatDto::setIsTeam));
            }
            if(Objects.nonNull(medal.getFriendNumber()) && !isFriend){
                futureList.add(supplyAsync(() -> glUserFriendsDao.getCountByMobileSha256(mobileSha256), userStatDto::setFriendCount));
            }
            if(Objects.nonNull(medal.getLikesNumber()) && !isLike){
                futureList.add(supplyAsync(() -> friendsRankFabDao.getLikeCount(mobileSha256), userStatDto::setLikeCount));
            }
            if(Objects.nonNull(medal.getLikedNumber()) && !isLiked){
                futureList.add(supplyAsync(() -> friendsRankFabDao.getLikedCount(mobileSha256), userStatDto::setLikedCount));
            }
            if(Objects.nonNull(medal.getClockInSumNumber()) && !isClockInSum){
                futureList.add(supplyAsync(() -> lowCarbonClockInDetailsDao.getCountByMobileSha256(mobileSha256), userStatDto::setClockInSumNumber));
            }
            if(Objects.nonNull(medal.getInviteNumber()) && !isInvite){
                futureList.add(supplyAsync(() -> {
                    LambdaQueryWrapper<GlPoints> glPointsLambdaQueryWrapper = Wrappers.lambdaQuery();
                    glPointsLambdaQueryWrapper.select(GlPoints::getId)
                            .eq(GlPoints::getType, PointsType.INVITE.getCode())
                            .eq(GlPoints::getMobileSha256, mobileSha256)
                            .orderByDesc(GlPoints::getId);
                    return glPointsService.count(glPointsLambdaQueryWrapper);
                }, userStatDto::setInviteNumber));
            }
        }
        try {
            // 等待所有异步任务完成
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("CompletableFuture.allOf Exception error.", e);
        }
        return userStatDto;
    }

    /**
     * 判断用户是否符合某个勋章的条件
     *
     * @param userStatDto 用户统计数据
     * @param medal 勋章对象
     * @return 用户是否符合勋章条件
     */
    private boolean isMedalEligible(UserStatDto userStatDto, Medal medal,Map<Long,Map<Long,Integer>> medalIdClockInCount) {
        if (medal.getClockIn() > 0 && !isClockInEligible(userStatDto.getClockInCount(), medal,medalIdClockInCount)) {
            return false;
        }

        if (Objects.nonNull(medal.getEmission()) && userStatDto.getEmission().compareTo(medal.getEmission()) < 0) {
            return false;
        }

        if (Objects.nonNull(medal.getCarbonPoints()) && userStatDto.getCarbonScore() < medal.getCarbonPoints()) {
            return false;
        }

        if (Objects.nonNull(medal.getShareNumber()) && userStatDto.getShareCount() < medal.getShareNumber()) {
            return false;
        }

        if (Objects.nonNull(medal.getTickoffNumber()) && userStatDto.getTickoffCount() < medal.getTickoffNumber()) {
            return false;
        }

        if (medal.getNeedTeam() > Available.NO.getState() && !userStatDto.getIsTeam()) {
            return false;
        }

        if (Objects.nonNull(medal.getFriendNumber()) && userStatDto.getFriendCount() < medal.getFriendNumber()) {
            return false;
        }

        if (Objects.nonNull(medal.getLikesNumber()) && userStatDto.getLikeCount() < medal.getLikesNumber()) {
            return false;
        }

        if (Objects.nonNull(medal.getLikedNumber()) && userStatDto.getLikedCount() < medal.getLikedNumber()) {
            return false;
        }

        if (Objects.nonNull(medal.getClockInSumNumber()) && userStatDto.getClockInSumNumber() < medal.getClockInSumNumber()) {
            return false;
        }

        if (Objects.nonNull(medal.getInviteNumber()) && userStatDto.getInviteNumber() < medal.getInviteNumber()) {
            return false;
        }
        return true;
    }

    /**
     * 判断用户是否符合打卡次数的条件
     *
     * @param clockInCount 用户的打卡次数统计
     * @param medal 勋章对象
     * @return 用户是否符合打卡次数条件
     */
    private boolean isClockInEligible(Map<Long, Integer> clockInCount, Medal medal,Map<Long,Map<Long,Integer>> medalIdClockInCount) {
        Map<Long, Integer> longIntegerMap = medalIdClockInCount.get(medal.getId());
        // 循环longIntegerMap
        for (Map.Entry<Long, Integer> entry : longIntegerMap.entrySet()) {
            Long clockInId = entry.getKey();
            Integer clockInNumber = entry.getValue();
            Integer count = clockInCount.get(clockInId);
            if(Objects.isNull(count) || count < clockInNumber){
                return false;
            }
        }
        return true;
    }

    /**
     * 授予用户勋章并处理相应的奖励
     * @param mobileSha256 手机号sha256
     * @param medal 勋章对象
     */
    private boolean awardMedal(String mobileSha256, Medal medal) {
        GlMedalUser glMedalUser = new GlMedalUser();
        glMedalUser.setMedalId(medal.getId());
        glMedalUser.setState(MedalUserState.NOT_LOOK.getState());
        glMedalUser.setMobileSha256(mobileSha256);

        try {
            glMedalUserDao.insert(glMedalUser);
        } catch (DuplicateKeyException e) {
            log.error("插入用户勋章失败重复,用户:{},勋章:{}", mobileSha256, medal.getName());
            return false;
        }

        // 如果勋章有奖励积分，则发放奖励积分
        if (Objects.nonNull(medal.getGrantPoints()) && medal.getGrantPoints() > 0) {
            glPointsService.grantMedalPoints(glMedalUser.getId(), medal.getId(), medal.getGrantPoints().doubleValue(), mobileSha256, "获得勋章“" + medal.getName() + "”");
        }
        // 增加勋章的获取人数
        medalDao.updateAddObtainNumber(medal.getId());
        return true;
    }

    private <R> CompletableFuture<R> supplyAsync(Supplier<R> supplier, Consumer<R> function){
        return CompletableFuture.supplyAsync(supplier,executorService).whenComplete((result, throwable) -> {
            function.accept(result);
            if (throwable != null) {
                log.error("supplyAsync error", throwable);
            }
        });
    }

    @Override
    public ActivityProgressVo activityProgress(String mobileSha256) {
        GlAppletConfig glAppletConfig = glAppletConfigDao.selectByKey(AppletConfigConstant.POINTS_CONFIG);
        PointsObtainDto pointsObtainDto = JSON.parseObject(glAppletConfig.getParamValue(), PointsObtainDto.class);

        ActivityProgressVo activityProgressVo = new ActivityProgressVo();
        activityProgressVo.setSharePoints(pointsObtainDto.getShareActivityPoints());

        if(StrUtil.isBlank(mobileSha256)){
            mobileSha256 = UserUtils.getMobileSha256();
        }
        GlRank glRank = glRankDao.selectById(mobileSha256);
        if(Objects.isNull(glRank)){
            activityProgressVo.setRadio(0D);
        }else {
            LambdaQueryWrapper<GlRank> glRankLambdaQueryWrapper = Wrappers.lambdaQuery();
            glRankLambdaQueryWrapper.orderByDesc(GlRank::getRank);
            glRankLambdaQueryWrapper.last("limit 1");
            GlRank glRankLast = glRankDao.selectOne(glRankLambdaQueryWrapper);
            // 根据排名计算超过了多少人
            activityProgressVo.setRadio(CalcUtil.calcRankRadio(glRank.getRank(),glRankLast.getRank()));
        }
        return activityProgressVo;
    }

    @Override
    public void shareProgress() {
        String mobileSha256 = UserUtils.getMobileSha256();
        if(shareActivityProgressMap.containsKey(mobileSha256)){
            throw new GicWxAppException("您点击分享过快");
        }
        shareActivityProgressMap.put(mobileSha256,true);
        LowCarbonClockInService bean = SpringContextUtils.getBean(LowCarbonClockInService.class);
        try {
            GlAppletConfig glAppletConfig = glAppletConfigDao.selectByKey(AppletConfigConstant.POINTS_CONFIG);
            PointsObtainDto pointsObtainDto = JSON.parseObject(glAppletConfig.getParamValue(), PointsObtainDto.class);

            GlAppletConfig shareOneDayMaxConfig = glAppletConfigDao.selectByKey(AppletConfigConstant.SHARE_NUMBER_CONFIG);
            Integer shareOneDayMax = Integer.valueOf(shareOneDayMaxConfig.getParamValue());

            String version = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);

            // 根据version 查询count条数
            Integer count = shareDao.getVersionCountByMobileSha256(mobileSha256,version);
            if(count > shareOneDayMax){
                return;
            }
            Share share = new Share();
            share.setType(ShareType.PROGRESS.getType());
            share.setSourceId(ShareType.PROGRESS.getType().longValue());
            share.setMobileSha256(mobileSha256);
            share.setPoints(pointsObtainDto.getShareActivityPoints());
            share.setVersion(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
            shareDao.insert(share);
            // 发放积分
            glPointsService.grantSharePoints(share.getId(),pointsObtainDto.getShareActivityPoints().doubleValue(),mobileSha256,"分享活动进度");

            bean.handlerDonation(mobileSha256,DonationType.CONTRIBUTION_POINTS.getCode(),BigDecimal.valueOf(pointsObtainDto.getShareActivityPoints()));

            bean.medalHandler(mobileSha256,UserUtils.getUserId());
        } catch (Exception e){
            log.error("分享活动进度异常",e);
        }finally {
            shareActivityProgressMap.remove(mobileSha256);
        }
    }

    @Override
    public boolean clockInJudge(Long clockInId) {
        String mobileSha256 = UserUtils.getMobileSha256();
        LowCarbonClockIn lowCarbonClockIn = lowCarbonClockInDao.selectById(clockInId);
        // 判断间隔时间
        LambdaQueryWrapper<LowCarbonClockInDetails> lastQueryWrapper = Wrappers.lambdaQuery();
        lastQueryWrapper.select(LowCarbonClockInDetails::getCreated);
        lastQueryWrapper.eq(LowCarbonClockInDetails::getMobileSha256, mobileSha256);
        lastQueryWrapper.eq(LowCarbonClockInDetails::getClockInId, clockInId);
        lastQueryWrapper.last("limit 1");
        lastQueryWrapper.orderByDesc(LowCarbonClockInDetails::getId);
        LowCarbonClockInDetails lastLowCarbonClockInDetails = lowCarbonClockInDetailsDao.selectOne(lastQueryWrapper);
        // 判断是否超过间隔时间
        return Objects.isNull(lastLowCarbonClockInDetails) ||
                DateUtil.between(lastLowCarbonClockInDetails.getCreated(), new Date(), DateUnit.MINUTE) >= lowCarbonClockIn.getGapTime() * 60;
    }

    @Override
    public List<IsNewMedalVo> judgeNewMedal() {
        String mobileSha256 = UserUtils.getMobileSha256();
        LambdaQueryWrapper<GlMedalUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(GlMedalUser::getMobileSha256, mobileSha256);
        queryWrapper.eq(GlMedalUser::getState, MedalUserState.NOT_LOOK.getState());
        queryWrapper.orderByDesc(GlMedalUser::getId);
        List<GlMedalUser> glMedalUsers = glMedalUserDao.selectList(queryWrapper);
        if(CollUtil.isEmpty(glMedalUsers)){
            return null;
        }
        List<Long> medalIds = glMedalUsers.stream().map(GlMedalUser::getMedalId).filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<Medal> medals = medalDao.selectBatchIds(medalIds);
        if(CollUtil.isEmpty(medals)){
            return null;
        }
        Map<Long, Medal> medalMap = medals.stream().collect(Collectors.toMap(Medal::getId, medal -> medal));
        Integer userCount = glUserDao.selectCount(null);

        List<IsNewMedalVo> isNewMedalVos = Lists.newArrayListWithCapacity(glMedalUsers.size());

        for (GlMedalUser glMedalUser : glMedalUsers) {
            Medal medal = medalMap.get(glMedalUser.getMedalId());
            if(Objects.isNull(medal)){
                continue;
            }
            IsNewMedalVo isNewMedalVo = new IsNewMedalVo();
            BeanUtil.copyProperties(medal, isNewMedalVo);
            // 计算获得该勋章的用户占比，保留两位小数，用勋章的已获得人数obtainNumber除以用户总数userCount
            if(Objects.nonNull(userCount) && userCount>0){
                BigDecimal obtainPercent = BigDecimal.valueOf(medal.getObtainNumber())
                        .divide(BigDecimal.valueOf(userCount),4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                isNewMedalVo.setObtainPercent(obtainPercent);
            }
            isNewMedalVos.add(isNewMedalVo);
        }
        List<Long> medalUserIds = glMedalUsers.stream().map(GlMedalUser::getId).collect(Collectors.toList());
        // 修改勋章状态为已查看
        LambdaUpdateWrapper<GlMedalUser> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.in(GlMedalUser::getId, medalUserIds);

        GlMedalUser updateMedalUser = new GlMedalUser();
        updateMedalUser.setState(MedalUserState.LOOK.getState());
        glMedalUserDao.update(updateMedalUser, updateWrapper);
        return isNewMedalVos;
    }

    @Override
    public void processUserClockInMedal(String secret, String mobileSha256) {
        if("9526d1de5cf74e9f96ed4548485bc4f1".equals(secret)){
            return;
        }
        LowCarbonClockInService bean = SpringContextUtils.getBean(LowCarbonClockInService.class);
        if(StrUtil.isNotBlank(mobileSha256)){
            LambdaQueryWrapper<GlUser> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.select(GlUser::getId, GlUser::getMobileSha256);
            queryWrapper.eq(GlUser::getMobileSha256, mobileSha256);
            GlUser glUser = glUserDao.selectOne(queryWrapper);
            bean.medalHandler(mobileSha256,glUser.getId());
            return;
        }
        LambdaQueryWrapper<GlUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(GlUser::getId, GlUser::getMobileSha256);
        List<GlUser> glUserList = glUserDao.selectList(queryWrapper);
        for (GlUser glUser : glUserList) {
            bean.medalHandler(glUser.getMobileSha256(),glUser.getId());
        }
    }

    @Override
    public LowCarbonClockInDetailVo clockInDetail(Long id) {
        LowCarbonClockIn lowCarbonClockIn = lowCarbonClockInDao.selectById(id);
        if(Objects.isNull(lowCarbonClockIn)){
            return new LowCarbonClockInDetailVo();
        }
        if(Objects.equals(lowCarbonClockIn.getDeleted(), Deleted.DELETED.getDeleted())){
            return new LowCarbonClockInDetailVo();
        }
        if(!Objects.equals(lowCarbonClockIn.getState(), PublishState.PUBLISH.getState())){
            return new LowCarbonClockInDetailVo();
        }
        LowCarbonClockInDetailVo lowCarbonClockInDetailVo = new LowCarbonClockInDetailVo();
        BeanUtil.copyProperties(lowCarbonClockIn, lowCarbonClockInDetailVo);

        // 判断间隔时间
        LambdaQueryWrapper<LowCarbonClockInDetails> lastQueryWrapper = Wrappers.lambdaQuery();
        lastQueryWrapper.select(LowCarbonClockInDetails::getCreated);
        lastQueryWrapper.eq(LowCarbonClockInDetails::getMobileSha256, UserUtils.getMobileSha256());
        lastQueryWrapper.eq(LowCarbonClockInDetails::getClockInId, id);
        lastQueryWrapper.last("limit 1");
        lastQueryWrapper.orderByDesc(LowCarbonClockInDetails::getId);
        LowCarbonClockInDetails lastLowCarbonClockInDetails = lowCarbonClockInDetailsDao.selectOne(lastQueryWrapper);
        if(Objects.nonNull(lastLowCarbonClockInDetails)){
            long between = DateUtil.between(lastLowCarbonClockInDetails.getCreated(), new Date(), DateUnit.MINUTE);
            if(between > 0){
                long minute = lowCarbonClockIn.getGapTime() * 60 - between;
                if(minute <= 0){
                    lowCarbonClockInDetailVo.setGapTime(0);
                    return lowCarbonClockInDetailVo;
                }
                // 分转小时，保留小数点
                BigDecimal gapTime = BigDecimal.valueOf(minute).divide(java.math.BigDecimal.valueOf(60),4, RoundingMode.HALF_UP).setScale(0, RoundingMode.CEILING);
                lowCarbonClockInDetailVo.setGapTime(gapTime.intValue());
            }
        }
        return lowCarbonClockInDetailVo;
    }

    @Override
    public CertificateVo certificate() {
        GlUser glUser = glUserDao.selectById(UserUtils.getUserId());
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        CertificateVo certificateVo = new CertificateVo();

        certificateVo.setUserNumber(StrUtil.padPre(UserUtils.getUserId().toString(), 5, "0"));
        BigDecimal emission = glBehaviorDao.getSumEmissionByMobileSha256(glUser.getMobileSha256());
        emission = Objects.isNull(emission)?BigDecimal.ZERO:emission;
        certificateVo.setEmission(emission);
        certificateVo.setEmissionFormat(CalcUtil.weightFormatEnglish(certificateVo.getEmission().doubleValue()));
        // 查询配捐
        List<DonationGroupDto> donationGroupDtoList = donationDao.getDonationGroup(UserUtils.getMobileSha256());
        if(CollUtil.isNotEmpty(donationGroupDtoList)){
            BigDecimal amount = donationGroupDtoList.stream()
                    .map(DonationGroupDto::getSumAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            certificateVo.setDonationAmount(amount);
        }
        certificateVo.setDateTime(DateUtil.format(new Date(),"yyyy.MM.dd"));
        GlAppletConfig config = glAppletConfigDao.selectOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey, Global.CONFIG_BACKGROUND_IMAGE));
        if(config != null){
            certificateVo.setCertificateUrl(config.getParamValue());
        }
        return certificateVo;
    }

    @Override
    public void guard(Long id) {
        String mobileSha256 = UserUtils.getMobileSha256();
        GlAppletConfig glAppletConfig = glAppletConfigDao.selectByKey(AppletConfigConstant.POINTS_CONFIG);
        PointsObtainDto pointsObtainDto = JSON.parseObject(glAppletConfig.getParamValue(), PointsObtainDto.class);
        BigDecimal activitySumEmission = pointsObtainDto.getCarbonEmissionLimit();

        BigDecimal emission = glBehaviorDao.getSumEmissionByMobileSha256(mobileSha256);
        emission = Objects.isNull(emission)?BigDecimal.ZERO:emission;

        if(emission.compareTo(activitySumEmission) < 0){
            throw new GicWxAppException("您还未满足条件");
        }
        LambdaQueryWrapper<DataRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(DataRecord::getId);
        queryWrapper.eq(DataRecord::getMobileSha256, mobileSha256);
        queryWrapper.eq(DataRecord::getType, DataRecordType.GUARD.getType());
        queryWrapper.orderByDesc(DataRecord::getId);
        queryWrapper.last("limit 1");
        DataRecord judgeDataRecord = dataRecordDao.selectOne(queryWrapper);
        if(Objects.nonNull(judgeDataRecord)){
            throw new GicWxAppException("您已守护");
        }
        String version = DateUtil.format(new Date(),DatePattern.PURE_DATE_PATTERN);
        Map<String,Long> clockInDto = Maps.newHashMap();
        clockInDto.put("id",id);
        DataRecord dataRecord = new DataRecord();
        dataRecord.setMobileSha256(mobileSha256);
        dataRecord.setDataJson(JSON.toJSONString(clockInDto));
        dataRecord.setType(DataRecordType.GUARD.getType());
        dataRecord.setVersion(version);
        dataRecordDao.insert(dataRecord);
    }

    @Override
    public CarbonBookVo carbonBook() {
        String mobileSha256 = UserUtils.getMobileSha256();
        CarbonBookVo carbonBookVo = new CarbonBookVo();
        BigDecimal sumEmission = glBehaviorDao.getSumEmissionByMobileSha256(mobileSha256);
        sumEmission = Objects.isNull(sumEmission)?BigDecimal.ZERO:sumEmission;
        carbonBookVo.setMyEmission(sumEmission);
        carbonBookVo.setMyEmissionText(CalcUtil.weightFormatEnglish(carbonBookVo.getMyEmission().doubleValue()));
        GlRank glRank = glRankDao.selectById(mobileSha256);
        if(Objects.nonNull(glRank) && glRank.getRank() > 0){
            carbonBookVo.setMyRank(glRank.getRank());
        }
        List<CarbonBookVo.Classify> classifies = lowCarbonClockInDao.getMyClassify(mobileSha256);
        classifies = CollUtil.isEmpty(classifies)?Lists.newArrayList():classifies;
        // 计算减排量总和
        BigDecimal classifySumEmission = classifies.stream().map(CarbonBookVo.Classify::getEmission).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        classifies.forEach(classify -> {
            if(Objects.nonNull(classify.getEmission())){
                // 计算占比
                BigDecimal ratio = classify.getEmission().divide(classifySumEmission, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                classify.setRatio(ratio);
                classify.setEmissionText(CalcUtil.weightFormatEnglish(classify.getEmission().doubleValue()));
            }else {
                classify.setRatio(BigDecimal.ZERO);
                classify.setEmissionText("0.00");
                classify.setEmission(BigDecimal.ZERO);
            }
        });
        carbonBookVo.setClassifies(classifies);
        return carbonBookVo;
    }

    @Override
    public CarbonBookDetailCountVo carbonBookDetail(Long id, Integer page) {
        CarbonBookDetailCountVo carbonBookDetailCountVo = new CarbonBookDetailCountVo();
        String mobileSha256 = UserUtils.getMobileSha256();

        Page<LowCarbonClockInDetails> pageObj = new Page<>(page,10);

        LambdaQueryWrapper<LowCarbonClockInDetails> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LowCarbonClockInDetails::getMobileSha256, mobileSha256);
        queryWrapper.eq(LowCarbonClockInDetails::getClockInId, id);
        queryWrapper.orderByDesc(LowCarbonClockInDetails::getId);
        Page<LowCarbonClockInDetails> lowCarbonClockInDetailsPage = lowCarbonClockInDetailsDao.selectPage(pageObj, queryWrapper);
        carbonBookDetailCountVo.setCount(lowCarbonClockInDetailsPage.getTotal());
        if(CollUtil.isNotEmpty(lowCarbonClockInDetailsPage.getRecords())){
            List<LowCarbonClockInDetails> lowCarbonClockInDetails = lowCarbonClockInDetailsPage.getRecords();
            List<CarbonBookDetailVo> carbonBookDetailVos = lowCarbonClockInDetails.stream()
                    .map(carbonClockInDetails -> {
                        CarbonBookDetailVo carbonBookDetailVo = new CarbonBookDetailVo();
                        carbonBookDetailVo.setEmission(carbonClockInDetails.getEmission());
                        if (Objects.nonNull(carbonClockInDetails.getEmission())) {
                            carbonBookDetailVo.setEmissionText(CalcUtil.weightFormatEnglish(carbonBookDetailVo.getEmission().doubleValue()));
                        }
                        carbonBookDetailVo.setDescription(carbonClockInDetails.getDescription());
                        carbonBookDetailVo.setEmissionTime(DateUtil.formatDateTime(carbonClockInDetails.getCreated()));
                        return carbonBookDetailVo;
                    })
                    .collect(Collectors.toList());
            carbonBookDetailCountVo.setCarbonBookDetailVos(carbonBookDetailVos);
        }
        return carbonBookDetailCountVo;
    }

    /**
     * 处理第二阶段检查逻辑
     * 检查用户是否满足进入第二阶段的条件，并处理基金发放逻辑
     *
     * @param mobileSha256 用户手机号SHA256值
     */
    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processSecondStageCheck(String mobileSha256) {
        try {
            GlAppletConfig glAppletConfig = glAppletConfigDao.selectByKey(AppletConfigConstant.POINTS_CONFIG);
            if (Objects.isNull(glAppletConfig)) {
                log.error("积分获得配置不存在");
                return;
            }
            PointsObtainDto pointsObtainDto = JSON.parseObject(glAppletConfig.getParamValue(), PointsObtainDto.class);
            if(StrUtil.isBlank(pointsObtainDto.getActivityEndTime())){
                log.error("未设置活动结束时间");
                return;
            }
            // 每多少千克获得一次基金或者次满足减排量后获得基金为空/小于0不做处理
            BigDecimal eachEmission = pointsObtainDto.getEachEmission(); // 基金发放间隔（KG）
            BigDecimal eachEmissionFund = pointsObtainDto.getEachEmissionFund(); // 每次基金发放金额
            if(Objects.isNull(eachEmission) || eachEmission.compareTo(BigDecimal.ZERO) <= 0
                    || Objects.isNull(eachEmissionFund) || eachEmissionFund.compareTo(BigDecimal.ZERO) <= 0){
                log.error("每多少千克获得一次基金或者次满足减排量后获得基金为空/小于0");
                return;
            }
            BigDecimal carbonEmissionLimit = pointsObtainDto.getCarbonEmissionLimit();// 第一阶段减排量阈值（KG）
            Date activityEndTime = DateUtil.parse(pointsObtainDto.getActivityEndTime()); // 活动结束时间
            log.info("开始处理第二阶段检查，用户: {}", mobileSha256);
            // 第一步：检查是否已记录进入第二阶段
            LambdaQueryWrapper<DataRecord> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(DataRecord::getMobileSha256, mobileSha256);
            queryWrapper.eq(DataRecord::getType, DataRecordType.PASS_FIRST_STAGE.getType());
            DataRecord existingRecord = dataRecordDao.selectOne(queryWrapper);

            if (Objects.isNull(existingRecord)) {
                // 首次进入第二阶段判断
                log.info("用户{}首次进入第二阶段判断", mobileSha256);
                handleFirstTimeSecondStageCheck(mobileSha256, carbonEmissionLimit,
                        eachEmission, activityEndTime, eachEmissionFund);
                log.info("第二阶段检查处理完成，用户: {}", mobileSha256);
                return;
            }

            // 已进入第二阶段，检查是否需要发放基金
            log.info("用户{}已进入第二阶段，检查基金发放条件", mobileSha256);
            handleExistingSecondStageCheck(mobileSha256, existingRecord, eachEmission, eachEmissionFund);
            log.info("第二阶段检查处理完成，用户: {}", mobileSha256);
        } catch (Exception e) {
            log.error("处理第二阶段检查时发生异常，用户: {}", mobileSha256, e);
            throw new GicWxAppException("处理第二阶段检查失败，请稍后重试");
        }
    }

    @Override
    public void executorCheck(String secret) {
        if(!"5a1b2c3d4e5f67890123456789012345".equals(secret)){
            log.error("密钥错误");
            return;
        }
        LowCarbonClockInService bean = SpringContextUtils.getBean(LowCarbonClockInService.class);
        /*
          1.获取所有用户，只需要获取所有用户中的mobileSha256字段即可
          2.循环所有用户，对每个用户进行检查
         */
        LambdaQueryWrapper<GlUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(GlUser::getMobileSha256);
        List<GlUser> glUsers = glUserDao.selectList(queryWrapper);
        glUsers.forEach(glUser -> bean.processBatchEmissionFund(glUser.getMobileSha256()));
    }

    /**
     * 处理首次进入第二阶段的检查逻辑
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param firstStageThreshold 第一阶段减排量阈值
     * @param fundInterval 基金发放间隔
     * @param activityEndTime 活动结束时间
     * @param fundAmountPerTime 每次基金发放金额
     */
    private void handleFirstTimeSecondStageCheck(String mobileSha256, BigDecimal firstStageThreshold,
                                               BigDecimal fundInterval, Date activityEndTime, BigDecimal fundAmountPerTime) {
        // 查询用户所有时间的总减排量
        BigDecimal totalEmission = glBehaviorDao.getSumEmissionByMobileSha256(mobileSha256);
        totalEmission = Objects.isNull(totalEmission) ? BigDecimal.ZERO : totalEmission;

        // 将减排量从克转换为千克
        BigDecimal totalEmissionKg = totalEmission.divide(new BigDecimal("1000"), 3, RoundingMode.HALF_UP);

        log.info("用户{}总减排量: {}KG", mobileSha256, totalEmissionKg);

        // 判断是否满足进入第二阶段的条件（减排量达标 或 活动结束）
        Date currentTime = new Date();
        boolean canEnterSecondStage = totalEmissionKg.compareTo(firstStageThreshold) >= 0 || currentTime.after(activityEndTime);

        if (!canEnterSecondStage) {
            log.info("用户{}减排量未满足条件且活动未结束，不进入第二阶段", mobileSha256);
            return;
        }

        // 满足条件：记录进入第二阶段
        log.info("用户{}满足进入第二阶段条件，当前减排量: {}KG", mobileSha256, totalEmissionKg);
        recordPassFirstStageWithEmission(mobileSha256, totalEmissionKg);

        // 立即检查基金发放逻辑
        checkAndIssueFunds(mobileSha256, totalEmissionKg, totalEmissionKg, fundInterval, fundAmountPerTime);
    }

    /**
     * 处理已进入第二阶段的检查逻辑
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param dataRecord 第二阶段记录
     * @param fundInterval 基金发放间隔
     * @param fundAmountPerTime 每次基金发放金额
     */
    private void handleExistingSecondStageCheck(String mobileSha256, DataRecord dataRecord,
                                              BigDecimal fundInterval, BigDecimal fundAmountPerTime) {
        // 获取进入第二阶段时的减排量
        BigDecimal entryEmissionKg = getEntryEmissionFromDataRecord(dataRecord);

        // 获取用户当前总减排量
        BigDecimal currentTotalEmission = glBehaviorDao.getSumEmissionByMobileSha256(mobileSha256);
        currentTotalEmission = Objects.isNull(currentTotalEmission) ? BigDecimal.ZERO : currentTotalEmission;

        // 将减排量从克转换为千克
        BigDecimal currentTotalEmissionKg = currentTotalEmission.divide(new BigDecimal("1000"), 3, RoundingMode.HALF_UP);

        log.info("用户{}进入第二阶段时减排量: {}KG，当前减排量: {}KG", mobileSha256, entryEmissionKg, currentTotalEmissionKg);

        // 检查基金发放逻辑
        checkAndIssueFunds(mobileSha256, entryEmissionKg, currentTotalEmissionKg, fundInterval, fundAmountPerTime);
    }

    /**
     * 从DataRecord中解析进入第二阶段时的减排量
     *
     * @param dataRecord 第二阶段记录
     * @return 进入时的减排量（千克）
     */
    private BigDecimal getEntryEmissionFromDataRecord(DataRecord dataRecord) {
        try {
            if (Objects.isNull(dataRecord) || StrUtil.isBlank(dataRecord.getDataJson())) {
                log.warn("第二阶段记录为空或dataJson为空，返回0");
                return BigDecimal.ZERO;
            }

            JSONObject jsonObject = JSON.parseObject(dataRecord.getDataJson());
            String emissionAtEntry = jsonObject.getString("emissionAtEntry");

            if (StrUtil.isBlank(emissionAtEntry)) {
                log.warn("第二阶段记录中emissionAtEntry为空，返回0");
                return BigDecimal.ZERO;
            }

            return new BigDecimal(emissionAtEntry);
        } catch (Exception e) {
            log.error("解析第二阶段记录时发生异常: {}", dataRecord.getDataJson(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 记录用户通过第一阶段（带减排量）
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param emissionKg 进入时的减排量（千克）
     */
    private void recordPassFirstStageWithEmission(String mobileSha256, BigDecimal emissionKg) {
        try {
            JSONObject dataJson = new JSONObject();
            dataJson.put("emissionAtEntry", emissionKg.toString());

            DataRecord dataRecord = new DataRecord();
            dataRecord.setMobileSha256(mobileSha256);
            dataRecord.setType(DataRecordType.PASS_FIRST_STAGE.getType());
            dataRecord.setDataJson(dataJson.toJSONString());
            dataRecord.setCreated(new Date());
            dataRecordDao.insert(dataRecord);

            log.info("成功记录用户{}通过第一阶段，进入时减排量: {}KG", mobileSha256, emissionKg);
        } catch (Exception e) {
            log.error("记录用户{}通过第一阶段时发生异常", mobileSha256, e);
            throw new GicWxAppException("记录通过第一阶段失败");
        }
    }

    /**
     * 获取用户最新一次基金发放记录
     *
     * @param mobileSha256 用户手机号SHA256值
     * @return 最新的基金发放记录，如果没有则返回null
     */
    private Donation getLatestFundDonation(String mobileSha256) {
        try {
            LambdaQueryWrapper<Donation> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(Donation::getMobileSha256, mobileSha256);
            queryWrapper.eq(Donation::getType, DonationType.SECOND_FUND.getCode());
            queryWrapper.orderByDesc(Donation::getId);
            queryWrapper.last("limit 1");

            return donationDao.selectOne(queryWrapper);
        } catch (Exception e) {
            log.error("查询用户{}最新基金发放记录时发生异常", mobileSha256, e);
            return null;
        }
    }

    /**
     * 检查并发放基金
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param entryEmissionKg 进入第二阶段时的减排量（千克）
     * @param currentEmissionKg 当前总减排量（千克）
     * @param fundInterval 基金发放间隔（千克）
     * @param fundAmountPerTime 每次基金发放金额
     */
    private void checkAndIssueFunds(String mobileSha256, BigDecimal entryEmissionKg, BigDecimal currentEmissionKg,
                                   BigDecimal fundInterval, BigDecimal fundAmountPerTime) {
        // 确定基准减排量
        BigDecimal baseEmissionKg = entryEmissionKg; // 默认使用进入第二阶段时的减排量

        // 查询最新一次基金发放记录
        Donation latestFundDonation = getLatestFundDonation(mobileSha256);

        if (Objects.nonNull(latestFundDonation) && StrUtil.isNotBlank(latestFundDonation.getNotes())) {
            try {
                // 如果有基金发放记录，使用上次发放时的减排量作为基准
                baseEmissionKg = new BigDecimal(latestFundDonation.getNotes());
                log.info("用户{}使用上次基金发放时的减排量作为基准: {}KG", mobileSha256, baseEmissionKg);
            } catch (NumberFormatException e) {
                log.warn("用户{}最新基金记录的减排量格式错误: {}，使用进入第二阶段时的减排量",
                    mobileSha256, latestFundDonation.getNotes());
                baseEmissionKg = entryEmissionKg;
            }
        } else {
            log.info("用户{}没有基金发放记录，使用进入第二阶段时的减排量作为基准: {}KG", mobileSha256, baseEmissionKg);
        }

        // 计算减排量差值
        BigDecimal emissionDiff = currentEmissionKg.subtract(baseEmissionKg);

        if (emissionDiff.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("用户{}当前减排量{}KG未超过基准减排量{}KG，无需发放基金",
                mobileSha256, currentEmissionKg, baseEmissionKg);
            return;
        }

        // 计算应发放基金次数
        int shouldIssueTimes = emissionDiff.divide(fundInterval, 0, RoundingMode.DOWN).intValue();

        if (shouldIssueTimes <= 0) {
            log.info("用户{}减排量增加{}KG，未达到基金发放间隔{}KG，无需发放基金",
                mobileSha256, emissionDiff, fundInterval);
            return;
        }

        log.info("用户{}减排量增加{}KG，基金发放间隔{}KG，需要发放{}次基金",
            mobileSha256, emissionDiff, fundInterval, shouldIssueTimes);

        // 发放基金
        issueFundMultipleTimes(mobileSha256, shouldIssueTimes, currentEmissionKg, fundAmountPerTime);
    }

    /**
     * 批量发放基金
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param times 发放次数
     * @param currentEmissionKg 当前总减排量（千克）
     * @param fundAmountPerTime 每次基金发放金额
     */
    private void issueFundMultipleTimes(String mobileSha256, int times, BigDecimal currentEmissionKg, BigDecimal fundAmountPerTime) {
        try {
            for (int i = 0; i < times; i++) {
                Donation donation = new Donation();
                donation.setMobileSha256(mobileSha256);
                donation.setType(DonationType.SECOND_FUND.getCode());
                donation.setAmount(fundAmountPerTime);
                donation.setNotes(currentEmissionKg.toString()); // 记录当前总减排量
                donation.setCreated(new Date());

                donationDao.insert(donation);

                log.info("成功为用户{}发放第{}次基金，金额: {}，当前减排量: {}KG",
                    mobileSha256, i + 1, fundAmountPerTime, currentEmissionKg);
            }

            log.info("成功为用户{}批量发放{}次基金，总金额: {}",
                mobileSha256, times, fundAmountPerTime.multiply(new BigDecimal(times)));
        } catch (Exception e) {
            log.error("为用户{}批量发放基金时发生异常", mobileSha256, e);
            throw new GicWxAppException("批量发放基金失败");
        }
    }

    /**
     * 处理基于减排量的基金发放
     * 检查用户是否进入第二阶段，并根据减排量发放基金
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param emission 减排量（克）
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processEmissionBasedFund(String mobileSha256, BigDecimal emission, Long detailId) {
        try {
            log.info("开始处理基于减排量的基金发放，用户: {}，减排量: {}克，详情ID: {}", mobileSha256, emission, detailId);
            GlAppletConfig glAppletConfig = glAppletConfigDao.selectByKey(AppletConfigConstant.POINTS_CONFIG);
            if (Objects.isNull(glAppletConfig)) {
                log.error("积分获得配置不存在");
                return;
            }
            PointsObtainDto pointsObtainDto = JSON.parseObject(glAppletConfig.getParamValue(), PointsObtainDto.class);
            if(StrUtil.isBlank(pointsObtainDto.getActivityEndTime())){
                log.error("未设置活动结束时间");
                return;
            }
            if(Objects.isNull(pointsObtainDto.getOneKgPoints())){
                log.error("请设置1克等于多少分");
                return;
            }
            // 业务常量定义
            BigDecimal SECOND_STAGE_EMISSION_THRESHOLD_KG = pointsObtainDto.getCarbonEmissionLimit(); // 80KG阈值
            BigDecimal FUND_RATE_PER_GRAM = pointsObtainDto.getOneKgPoints(); // 每克0.0137分
            BigDecimal GRAMS_TO_KG = new BigDecimal("1000"); // 克转千克
            Date activityEndTime = DateUtil.parse(pointsObtainDto.getActivityEndTime());

            // 第一步：检查是否已进入第二阶段
            LambdaQueryWrapper<DataRecord> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(DataRecord::getMobileSha256, mobileSha256);
            queryWrapper.eq(DataRecord::getType, DataRecordType.ENTER_SECOND_STAGE.getType());
            DataRecord existingRecord = dataRecordDao.selectOne(queryWrapper);

            if (Objects.isNull(existingRecord)) {
                // 没有进入第二阶段，需要判断是否满足进入条件
                log.info("用户{}尚未进入第二阶段，开始判断进入条件", mobileSha256);
                handleEnterSecondStageCheck(mobileSha256, SECOND_STAGE_EMISSION_THRESHOLD_KG, GRAMS_TO_KG, activityEndTime);
            }

            // 检查是否已进入第二阶段（可能刚刚进入）
            DataRecord secondStageRecord = dataRecordDao.selectOne(queryWrapper);
            if (Objects.nonNull(secondStageRecord)) {
                // 已进入第二阶段，发放基金
                log.info("用户{}已进入第二阶段，开始发放基金", mobileSha256);
                BigDecimal fundAmount = emission.multiply(FUND_RATE_PER_GRAM);
                issueEmissionBasedFund(mobileSha256, emission, detailId, fundAmount);
            }

            log.info("基于减排量的基金发放处理完成，用户: {}", mobileSha256);
        } catch (Exception e) {
            log.error("处理基于减排量的基金发放时发生异常，用户: {}", mobileSha256, e);
            throw new GicWxAppException("处理基于减排量的基金发放失败，请稍后重试");
        }
    }

    /**
     * 处理进入第二阶段的检查逻辑
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param thresholdKg 减排量阈值（千克）
     * @param gramsToKg 克转千克的转换系数
     * @param activityEndTime 活动结束时间
     */
    private void handleEnterSecondStageCheck(String mobileSha256, BigDecimal thresholdKg,
                                           BigDecimal gramsToKg, Date activityEndTime) {
        // 查询用户总减排量
        BigDecimal totalEmissionGrams = glBehaviorDao.getSumEmissionByMobileSha256(mobileSha256);
        totalEmissionGrams = Objects.isNull(totalEmissionGrams) ? BigDecimal.ZERO : totalEmissionGrams;

        // 将减排量从克转换为千克
        BigDecimal totalEmissionKg = totalEmissionGrams.divide(gramsToKg, 3, RoundingMode.HALF_UP);

        log.info("用户{}总减排量: {}克 ({}KG)", mobileSha256, totalEmissionGrams, totalEmissionKg);

        // 判断是否满足进入第二阶段的条件（减排量达标 或 活动结束）
        Date currentTime = new Date();
        boolean canEnterSecondStage = totalEmissionKg.compareTo(thresholdKg) >= 0 || currentTime.after(activityEndTime);

        if (!canEnterSecondStage) {
            log.info("用户{}减排量未满足条件且活动未结束，不进入第二阶段", mobileSha256);
            return;
        }

        // 满足条件：记录进入第二阶段
        log.info("用户{}满足进入第二阶段条件，总减排量: {}克", mobileSha256, totalEmissionGrams);
        recordEnterSecondStage(mobileSha256, totalEmissionGrams);
    }

    /**
     * 记录用户进入第二阶段
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param totalEmissionGrams 用户总减排量（克）
     */
    private void recordEnterSecondStage(String mobileSha256, BigDecimal totalEmissionGrams) {
        try {
            JSONObject dataJson = new JSONObject();
            dataJson.put("totalEmissionGrams", totalEmissionGrams.toString());

            DataRecord dataRecord = new DataRecord();
            dataRecord.setMobileSha256(mobileSha256);
            dataRecord.setType(DataRecordType.ENTER_SECOND_STAGE.getType());
            dataRecord.setDataJson(dataJson.toJSONString());
            dataRecord.setCreated(new Date());
            dataRecordDao.insert(dataRecord);

            log.info("成功记录用户{}进入第二阶段，总减排量: {}克", mobileSha256, totalEmissionGrams);
        } catch (Exception e) {
            log.error("记录用户{}进入第二阶段时发生异常", mobileSha256, e);
            throw new GicWxAppException("记录进入第二阶段失败");
        }
    }

    /**
     * 发放基于减排量的基金
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param currentEmissionGrams 本次减排量（克）
     * @param detailId 打卡详情ID
     * @param fundAmount 基金金额
     */
    private void issueEmissionBasedFund(String mobileSha256, BigDecimal currentEmissionGrams, Long detailId, BigDecimal fundAmount) {
        try {
            Donation donation = new Donation();
            donation.setMobileSha256(mobileSha256);
            donation.setType(DonationType.FUND.getCode()); // 使用值为2的枚举
            donation.setAmount(fundAmount);
            donation.setNotes(detailId + "," + currentEmissionGrams.toString()); // detailId,减排量
            donation.setCreated(new Date());

            donationDao.insert(donation);

            log.info("成功为用户{}发放基于减排量的基金，详情ID: {}，减排量: {}克，金额: {}分",
                mobileSha256, detailId, currentEmissionGrams, fundAmount);
        } catch (Exception e) {
            log.error("为用户{}发放基于减排量的基金时发生异常，详情ID: {}", mobileSha256, detailId, e);
            throw new GicWxAppException("发放基于减排量的基金失败");
        }
    }

    /**
     * 批量处理基于减排量的基金发放
     * 根据打卡详情记录批量发放基金
     *
     * @param mobileSha256 用户手机号SHA256值
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processBatchEmissionFund(String mobileSha256) {
        try {
            log.info("开始批量处理基于减排量的基金发放，用户: {}", mobileSha256);
            GlAppletConfig glAppletConfig = glAppletConfigDao.selectByKey(AppletConfigConstant.POINTS_CONFIG);
            if (Objects.isNull(glAppletConfig)) {
                log.error("积分获得配置不存在");
                return;
            }
            PointsObtainDto pointsObtainDto = JSON.parseObject(glAppletConfig.getParamValue(), PointsObtainDto.class);
            if(StrUtil.isBlank(pointsObtainDto.getActivityEndTime())){
                log.error("未设置活动结束时间");
                return;
            }
            if(Objects.isNull(pointsObtainDto.getOneKgPoints())){
                log.error("请设置1克等于多少分");
                return;
            }

            // 第一步：查询发放进度记录
            Long lastDetailId = getLatestFundProgress(mobileSha256);

            // 第二步：根据进度记录查询需要发放基金的数据
            List<LowCarbonClockInDetails> detailsList;
            if (Objects.isNull(lastDetailId)) {
                // 首次批量发放：查询所有数据
                log.info("用户{}首次批量发放基金", mobileSha256);
                detailsList = queryAllClockInDetails(mobileSha256);
            } else {
                // 增量发放：查询新增数据
                log.info("用户{}增量发放基金，上次最后ID: {}", mobileSha256, lastDetailId);
                detailsList = queryIncrementalClockInDetails(mobileSha256, lastDetailId);
            }

            // 第三步：检查是否有数据需要处理
            if (Objects.isNull(detailsList) || detailsList.isEmpty()) {
                log.info("用户{}没有需要发放基金的数据，跳过处理", mobileSha256);
                return;
            }

            // 第四步：循环发放基金
            log.info("用户{}开始批量发放基金，共{}条记录", mobileSha256, detailsList.size());
            for (LowCarbonClockInDetails detail : detailsList) {
                processEmissionBasedFundForBatch(mobileSha256, detail.getEmission(), detail.getId(),pointsObtainDto);
            }

            // 第五步：记录发放进度
            Long newLastDetailId = detailsList.get(detailsList.size() - 1).getId();
            recordFundProgress(mobileSha256, newLastDetailId);

            log.info("批量处理基于减排量的基金发放完成，用户: {}，处理记录数: {}，最新ID: {}",
                mobileSha256, detailsList.size(), newLastDetailId);
        } catch (Exception e) {
            log.error("批量处理基于减排量的基金发放时发生异常，用户: {}", mobileSha256, e);
            throw new GicWxAppException("批量处理基于减排量的基金发放失败，请稍后重试");
        }
    }

    /**
     * 获取最新的基金发放进度
     *
     * @param mobileSha256 用户手机号SHA256值
     * @return 上次发放的最后一条详情ID，如果没有记录则返回null
     */
    private Long getLatestFundProgress(String mobileSha256) {
        try {
            LambdaQueryWrapper<DataRecord> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(DataRecord::getMobileSha256, mobileSha256);
            queryWrapper.eq(DataRecord::getType, DataRecordType.FUND_PROGRESS.getType());
            queryWrapper.orderByDesc(DataRecord::getId);
            queryWrapper.last("limit 1");

            DataRecord progressRecord = dataRecordDao.selectOne(queryWrapper);
            if (Objects.isNull(progressRecord) || StrUtil.isBlank(progressRecord.getDataJson())) {
                return null;
            }

            JSONObject jsonObject = JSON.parseObject(progressRecord.getDataJson());
            String lastDetailIdStr = jsonObject.getString("lastDetailId");
            return StrUtil.isNotBlank(lastDetailIdStr) ? Long.valueOf(lastDetailIdStr) : null;
        } catch (Exception e) {
            log.error("获取用户{}最新基金发放进度时发生异常", mobileSha256, e);
            return null;
        }
    }

    /**
     * 查询所有打卡详情数据
     *
     * @param mobileSha256 用户手机号SHA256值
     * @return 打卡详情列表
     */
    private List<LowCarbonClockInDetails> queryAllClockInDetails(String mobileSha256) {
        LambdaQueryWrapper<LowCarbonClockInDetails> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LowCarbonClockInDetails::getMobileSha256, mobileSha256);
        queryWrapper.eq(LowCarbonClockInDetails::getSource, 1);
        queryWrapper.orderByAsc(LowCarbonClockInDetails::getId);

        return lowCarbonClockInDetailsDao.selectList(queryWrapper);
    }

    /**
     * 查询增量打卡详情数据
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param lastDetailId 上次处理的最后一条记录ID
     * @return 打卡详情列表
     */
    private List<LowCarbonClockInDetails> queryIncrementalClockInDetails(String mobileSha256, Long lastDetailId) {
        LambdaQueryWrapper<LowCarbonClockInDetails> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LowCarbonClockInDetails::getMobileSha256, mobileSha256);
        queryWrapper.eq(LowCarbonClockInDetails::getSource, 1);
        queryWrapper.gt(LowCarbonClockInDetails::getId, lastDetailId);
        queryWrapper.orderByAsc(LowCarbonClockInDetails::getId);

        return lowCarbonClockInDetailsDao.selectList(queryWrapper);
    }

    /**
     * 记录基金发放进度
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param lastDetailId 最后一条处理的详情ID
     */
    private void recordFundProgress(String mobileSha256, Long lastDetailId) {
        try {
            JSONObject dataJson = new JSONObject();
            dataJson.put("lastDetailId", lastDetailId.toString());

            DataRecord dataRecord = new DataRecord();
            dataRecord.setMobileSha256(mobileSha256);
            dataRecord.setType(DataRecordType.FUND_PROGRESS.getType());
            dataRecord.setDataJson(dataJson.toJSONString());
            dataRecord.setCreated(new Date());
            dataRecordDao.insert(dataRecord);

            log.info("成功记录用户{}基金发放进度，最后ID: {}", mobileSha256, lastDetailId);
        } catch (Exception e) {
            log.error("记录用户{}基金发放进度时发生异常", mobileSha256, e);
            throw new GicWxAppException("记录基金发放进度失败");
        }
    }

    /**
     * 处理基于减排量的基金发放（用于批量处理）
     * 不带事务注解，同步处理，专门用于批量发放中的单次调用
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param emission 减排量（克）
     * @param detailId 打卡详情ID
     */
    private void processEmissionBasedFundForBatch(String mobileSha256, BigDecimal emission, Long detailId,PointsObtainDto pointsObtainDto) {
        try {
            log.debug("开始处理单次基金发放，用户: {}，减排量: {}克，详情ID: {}", mobileSha256, emission, detailId);

            // 业务常量定义
            BigDecimal SECOND_STAGE_EMISSION_THRESHOLD_KG = pointsObtainDto.getCarbonEmissionLimit(); // 80KG阈值
            BigDecimal FUND_RATE_PER_GRAM = pointsObtainDto.getOneKgPoints(); // 每克0.0137分
            BigDecimal GRAMS_TO_KG = new BigDecimal("1000"); // 克转千克
            Date activityEndTime = DateUtil.parse(pointsObtainDto.getActivityEndTime());

            // 第一步：检查是否已进入第二阶段
            LambdaQueryWrapper<DataRecord> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(DataRecord::getMobileSha256, mobileSha256);
            queryWrapper.eq(DataRecord::getType, DataRecordType.ENTER_SECOND_STAGE.getType());
            DataRecord existingRecord = dataRecordDao.selectOne(queryWrapper);

            if (Objects.isNull(existingRecord)) {
                // 没有进入第二阶段，需要判断是否满足进入条件
                log.debug("用户{}尚未进入第二阶段，开始判断进入条件", mobileSha256);
                handleEnterSecondStageCheckForBatch(mobileSha256, SECOND_STAGE_EMISSION_THRESHOLD_KG, GRAMS_TO_KG, activityEndTime);
            }

            // 检查是否已进入第二阶段（可能刚刚进入）
            DataRecord secondStageRecord = dataRecordDao.selectOne(queryWrapper);
            if (Objects.nonNull(secondStageRecord)) {
                // 已进入第二阶段，发放基金
                log.debug("用户{}已进入第二阶段，开始发放基金", mobileSha256);
                BigDecimal fundAmount = emission.multiply(FUND_RATE_PER_GRAM);
                issueEmissionBasedFundForBatch(mobileSha256, emission, detailId, fundAmount);
            }

            log.debug("单次基金发放处理完成，用户: {}，详情ID: {}", mobileSha256, detailId);
        } catch (Exception e) {
            log.error("处理单次基金发放时发生异常，用户: {}，详情ID: {}", mobileSha256, detailId, e);
            throw new GicWxAppException("处理单次基金发放失败");
        }
    }

    /**
     * 处理进入第二阶段的检查逻辑（用于批量处理）
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param thresholdKg 减排量阈值（千克）
     * @param gramsToKg 克转千克的转换系数
     * @param activityEndTime 活动结束时间
     */
    private void handleEnterSecondStageCheckForBatch(String mobileSha256, BigDecimal thresholdKg,
                                                   BigDecimal gramsToKg, Date activityEndTime) {
        // 查询用户总减排量
        BigDecimal totalEmissionGrams = glBehaviorDao.getSumEmissionByMobileSha256(mobileSha256);
        totalEmissionGrams = Objects.isNull(totalEmissionGrams) ? BigDecimal.ZERO : totalEmissionGrams;

        // 将减排量从克转换为千克
        BigDecimal totalEmissionKg = totalEmissionGrams.divide(gramsToKg, 3, RoundingMode.HALF_UP);

        log.debug("用户{}总减排量: {}克 ({}KG)", mobileSha256, totalEmissionGrams, totalEmissionKg);

        // 判断是否满足进入第二阶段的条件（减排量达标 或 活动结束）
        Date currentTime = new Date();
        boolean canEnterSecondStage = totalEmissionKg.compareTo(thresholdKg) >= 0 || currentTime.after(activityEndTime);

        if (!canEnterSecondStage) {
            log.debug("用户{}减排量未满足条件且活动未结束，不进入第二阶段", mobileSha256);
            return;
        }

        // 满足条件：记录进入第二阶段
        log.debug("用户{}满足进入第二阶段条件，总减排量: {}克", mobileSha256, totalEmissionGrams);
        recordEnterSecondStageForBatch(mobileSha256, totalEmissionGrams);
    }

    /**
     * 记录用户进入第二阶段（用于批量处理）
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param totalEmissionGrams 用户总减排量（克）
     */
    private void recordEnterSecondStageForBatch(String mobileSha256, BigDecimal totalEmissionGrams) {
        try {
            JSONObject dataJson = new JSONObject();
            dataJson.put("totalEmissionGrams", totalEmissionGrams.toString());

            DataRecord dataRecord = new DataRecord();
            dataRecord.setMobileSha256(mobileSha256);
            dataRecord.setType(DataRecordType.ENTER_SECOND_STAGE.getType());
            dataRecord.setDataJson(dataJson.toJSONString());
            dataRecord.setCreated(new Date());
            dataRecordDao.insert(dataRecord);

            log.debug("成功记录用户{}进入第二阶段，总减排量: {}克", mobileSha256, totalEmissionGrams);
        } catch (Exception e) {
            log.error("记录用户{}进入第二阶段时发生异常", mobileSha256, e);
            throw new GicWxAppException("记录进入第二阶段失败");
        }
    }

    /**
     * 发放基于减排量的基金（用于批量处理）
     *
     * @param mobileSha256 用户手机号SHA256值
     * @param currentEmissionGrams 本次减排量（克）
     * @param detailId 打卡详情ID
     * @param fundAmount 基金金额
     */
    private void issueEmissionBasedFundForBatch(String mobileSha256, BigDecimal currentEmissionGrams,
                                              Long detailId, BigDecimal fundAmount) {
        try {
            Donation donation = new Donation();
            donation.setMobileSha256(mobileSha256);
            donation.setType(DonationType.FUND.getCode()); // 使用值为2的枚举
            donation.setAmount(fundAmount);
            donation.setNotes(detailId + "," + currentEmissionGrams.toString()); // detailId,减排量
            donation.setCreated(new Date());

            donationDao.insert(donation);

            log.debug("成功为用户{}发放基于减排量的基金，详情ID: {}，减排量: {}克，金额: {}分",
                mobileSha256, detailId, currentEmissionGrams, fundAmount);
        } catch (Exception e) {
            log.error("为用户{}发放基于减排量的基金时发生异常，详情ID: {}", mobileSha256, detailId, e);
            throw new GicWxAppException("发放基于减排量的基金失败");
        }
    }
}