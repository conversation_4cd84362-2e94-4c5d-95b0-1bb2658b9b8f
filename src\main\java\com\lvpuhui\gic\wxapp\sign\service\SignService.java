package com.lvpuhui.gic.wxapp.sign.service;

import com.lvpuhui.gic.wxapp.sign.dto.*;
import com.lvpuhui.gic.wxapp.sign.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SignService {
    SignClassifyTrifleListVo signClassIfyTrifle();

    Integer saveSign(SignSaveDto signSaveDto);

    List<LitPictureListVo> litPicture();

    Integer suppleSign(SuppleSignSaveDto suppleSignSaveDto);

    List<SignListVo> getSignList(SignListDto signListDto);

    GetIsRewardVo getIsReward();

    RewardPointsVo getRewardsPoints();

    void getMyPoints(SupplePointsDto supplePointsDto);

    PointsAndDaysVo getAllSuppleDaysAndPoints();

    /**
     * 我的小事徽章接口
     */
    MySignVo myLight();
}
