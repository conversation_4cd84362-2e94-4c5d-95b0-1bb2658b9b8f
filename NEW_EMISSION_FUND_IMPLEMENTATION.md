# 基于减排量的基金发放功能实现

## 概述
新增了一个独立的基于减排量的基金发放功能，与之前的第二阶段检查功能并行存在，不影响原有代码。

## 修改内容

### 1. 枚举类修改
#### DataRecordType.java
```java
ENTER_SECOND_STAGE(2,"进入第二阶段"), // 新增
```

### 2. 接口方法
#### LowCarbonClockInService.java
```java
/**
 * 处理基于减排量的基金发放
 * 检查用户是否进入第二阶段，并根据减排量发放基金
 * 
 * @param mobileSha256 用户手机号SHA256值
 * @param emission 减排量（克）
 */
void processEmissionBasedFund(String mobileSha256, BigDecimal emission);
```

### 3. 实现类方法
#### LowCarbonClockInServiceImpl.java

##### 主方法
- `processEmissionBasedFund(String mobileSha256, BigDecimal emission)` - 主入口方法

##### 私有方法
- `handleEnterSecondStageCheck()` - 处理进入第二阶段的检查逻辑
- `recordEnterSecondStage()` - 记录用户进入第二阶段
- `issueEmissionBasedFund()` - 发放基于减排量的基金

## 业务逻辑

### 进入第二阶段条件
1. **减排量达标**：用户总减排量 >= 80KG
2. **活动结束**：当前时间 > 活动结束时间

### 基金发放规则
- **计算公式**：减排量（克） × 0.0137分 = 基金金额
- **发放条件**：用户已进入第二阶段
- **重复发放**：每次调用都发放，不做重复检查

### 数据存储

#### gl_data_record表
- **type**: 2 (ENTER_SECOND_STAGE)
- **dataJson**: `{"totalEmissionGrams": "150000"}` - 进入时用户总减排量（克）

#### gl_donation表
- **type**: 2 (FUND)
- **amount**: 基金金额（分）
- **notes**: 本次传入的减排量参数值（克）

## 关键参数

```java
final BigDecimal SECOND_STAGE_EMISSION_THRESHOLD_KG = new BigDecimal("80"); // 80KG阈值
final BigDecimal FUND_RATE_PER_GRAM = new BigDecimal("0.0137"); // 每克0.0137分
final BigDecimal GRAMS_TO_KG = new BigDecimal("1000"); // 克转千克
final Date activityEndTime = new Date(); // TODO: 活动结束时间变量，后续需要从配置中获取
```

## 使用示例

```java
@Autowired
private LowCarbonClockInService lowCarbonClockInService;

// 调用基于减排量的基金发放
public void processFund(String mobileSha256, BigDecimal emission) {
    try {
        lowCarbonClockInService.processEmissionBasedFund(mobileSha256, emission);
        log.info("基金发放处理完成，用户: {}, 减排量: {}克", mobileSha256, emission);
    } catch (Exception e) {
        log.error("基金发放处理失败，用户: {}", mobileSha256, e);
    }
}
```

## 执行流程

1. **检查进入状态**：查询用户是否已进入第二阶段
2. **判断进入条件**：如果未进入，检查是否满足进入条件
3. **记录进入信息**：满足条件则记录进入第二阶段
4. **发放基金**：如果已进入第二阶段，根据减排量发放基金

## 特点

1. **独立性**：与原有代码完全独立，不影响现有功能
2. **事务性**：使用@Transactional确保数据一致性
3. **日志完整**：详细的日志记录便于问题排查
4. **异常处理**：完善的异常处理机制
5. **参数化**：关键参数独立定义，便于后续调整

## 待配置项

1. **活动结束时间**：需要从配置中获取实际值
2. **基金费率**：0.0137分/克，可考虑配置化
3. **减排量阈值**：80KG，可考虑配置化

## 注意事项

1. 减排量单位：传入参数为克，判断阈值时转换为千克
2. 基金金额单位：计算结果为分，直接存储
3. 重复发放：每次调用都发放，调用方需要控制重复
4. 数据记录：gl_data_record记录总减排量，gl_donation记录本次减排量

## 编译状态

✅ 所有代码编译通过，无错误和警告（除TODO注释外）
