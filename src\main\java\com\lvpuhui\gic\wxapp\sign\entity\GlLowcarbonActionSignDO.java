package com.lvpuhui.gic.wxapp.sign.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 低碳小事签到表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gl_lowcarbon_action_sign")
public class GlLowcarbonActionSignDO extends Model<GlLowcarbonActionSignDO> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户手机号hash
     */
    private String mobileSha256;

    /**
     * 累计签到天数
     */
    private Integer signTotal;

    /**
     * 连续签到的天数
     */
    private Integer repeatSignTotal;

    /**
     * 最后一次签到的日期
     */
    private LocalDate lastSignDate;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
