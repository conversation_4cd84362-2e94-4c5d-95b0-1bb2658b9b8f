<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.dao.TodayEmissionDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.entity.TodayEmission">
    <!--@mbg.generated-->
    <!--@Table gl_today_emission-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mobile_sha256" jdbcType="VARCHAR" property="mobileSha256" />
    <result column="emission" jdbcType="DECIMAL" property="emission" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, mobile_sha256, emission, version, created
  </sql>

  <insert id="insertOnUpdate">
    insert into gl_today_emission (mobile_sha256, emission, version)
    values (#{todayEmission.mobileSha256,jdbcType=VARCHAR}, #{todayEmission.emission,jdbcType=DECIMAL}, #{todayEmission.version,jdbcType=VARCHAR})
    on duplicate key update emission = emission + VALUES(emission)
  </insert>

  <select id="getTodayRank" resultType="com.lvpuhui.gic.wxapp.entity.vo.TodayRankVo">
    SELECT
      gte.mobile_sha256,
      gu.avatar_url,
      gu.nick_name,
      gte.emission
    FROM
      gl_today_emission gte
        INNER JOIN gl_user gu ON gte.mobile_sha256 = gu.mobile_sha256
    WHERE
      gte.version = #{version}
    ORDER BY
      gte.emission DESC,gte.created ASC
    LIMIT 10
  </select>

  <select id="getTodayRankFirstUser" resultType="com.lvpuhui.gic.wxapp.entity.vo.RankFirstVo$RankFirstUserVo">
    SELECT
      gu.avatar_url,
      gu.nick_name
    FROM
      gl_today_emission gte
        INNER JOIN gl_user gu ON gte.mobile_sha256 = gu.mobile_sha256
    WHERE
      gte.version = #{version}
    ORDER BY
      gte.emission DESC,gte.created ASC
    LIMIT 1
  </select>
</mapper>