package com.lvpuhui.gic.wxapp.service;

import com.lvpuhui.gic.wxapp.entity.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CarbonBookService {
    /**
     * 我的积分减排量
     * @param mobileSha256
     * @return
     */
    PointsEmission myPointsEmission(String mobileSha256);

    /**
     * 碳账本
     * @param mobileSha256
     * @return
     */
    CarbonBooksVo getCarbonBooks(String mobileSha256);

    /**
     * 获取场景减排明细
     * @param mobileSha256
     * @param sceneId
     * @param page
     * @param size
     * @return
     */
    SceneTotalVo getSceneDetails(String mobileSha256, Integer sceneId, Integer page, Integer size);

    /**
     * 碳账本接口
     */
    CarbonBooksNewVo carbonBooksMobile();

    /**
     * 碳账本减排明细接口
     */
    CarbonBooksDetailNewVo carbonBooksMobileDetail(Integer actId, Integer page, Integer size);

    CarbonBooksVo getCarbonBooksByMobileSha256(String mobileSha256);
}
