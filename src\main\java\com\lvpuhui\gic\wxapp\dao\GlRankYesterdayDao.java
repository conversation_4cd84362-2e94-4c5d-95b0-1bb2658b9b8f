package com.lvpuhui.gic.wxapp.dao;

import com.lvpuhui.gic.wxapp.entity.GlRankYesterday;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.entity.po.RankYesterdayList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 昨日排行榜 Mapper 接口
 * <AUTHOR>
 * @since 2022-07-25
 */
public interface GlRankYesterdayDao extends BaseMapper<GlRankYesterday> {

    /**
     * 获取前20昨日排行用户信息
     */
    @Select("SELECT " +
            "gry.rank, " +
            "gry.emission, " +
            "gu.nick_name, " +
            "gu.avatar_url, " +
            "gu.mobile_sha256, " +
            "grd.days as rankDays " +
            "FROM " +
            "gl_rank_yesterday gry " +
            "INNER JOIN gl_user gu ON gry.mobile_sha256 = gu.mobile_sha256  " +
            "LEFT JOIN gl_rank_days grd ON gry.mobile_sha256 = grd.mobile_sha256 " +
            "ORDER BY gry.rank " +
            "LIMIT 20")
    List<RankYesterdayList.YesterdayRankUser> getYesterdayRankUserInfo();

    /**
     * 根据用户查询具体的排行
     */
    @Select("SELECT " +
            "gry.rank, " +
            "gry.emission, " +
            "gu.nick_name, " +
            "gu.avatar_url, " +
            "gu.mobile_sha256, " +
            "grd.days as rankDays " +
            "FROM " +
            "gl_rank_yesterday gry " +
            "INNER JOIN gl_user gu ON gry.mobile_sha256 = gu.mobile_sha256  " +
            "LEFT JOIN gl_rank_days grd ON gry.mobile_sha256 = grd.mobile_sha256 " +
            "WHERE gry.mobile_sha256 = #{mobileSha256}")
    RankYesterdayList.YesterdayRankUser getRankUserInfoBySha256(@Param("mobileSha256") String mobileSha256);
}
