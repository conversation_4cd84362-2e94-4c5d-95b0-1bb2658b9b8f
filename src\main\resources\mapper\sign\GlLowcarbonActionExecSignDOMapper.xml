<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.sign.dao.GlLowcarbonActionExecSignDao">

    <select id="getTodayData" resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionExecSignDO">
        select id,user_id as userId,mobile_sha256 as mobileSha256,execute_date as executeDate,action_id as actionId,updated_at as updatedAt,created_at as createdAt from gl_lowcarbon_action_exec_sign where user_id = #{userId} and action_id = #{trifleId} and execute_date = #{today}
    </select>
    <select id="selectByUserAndTri" resultType="com.lvpuhui.gic.wxapp.sign.entity.GlLowcarbonActionExecSignDO">
        select id,user_id as userId,mobile_sha256 as mobileSha256,execute_date as executeDate,action_id as actionId,updated_at as updatedAt,created_at as createdAt from gl_lowcarbon_action_exec_sign where user_id = #{userId} and action_id = #{actionId}
    </select>

    <select id="getMyLight" resultType="java.lang.String">
        SELECT
            gla.icon_light
        FROM
            gl_lowcarbon_action gla
                INNER JOIN gl_lowcarbon_action_exec_sign glaes ON gla.id = glaes.action_id
        WHERE
            glaes.mobile_sha256 = #{mobileSha256}
        ORDER BY
            gla.sort
    </select>
</mapper>
