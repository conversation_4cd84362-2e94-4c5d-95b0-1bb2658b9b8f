package com.lvpuhui.gic.wxapp.team.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.team.entity.GlTeamMember;
import org.apache.ibatis.annotations.Param;

/**
 * 战队成员表(GlTeamMember)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-24 11:24:25
 */
public interface GlTeamMemberDao extends BaseMapper<GlTeamMember> {

    int getExistByUserId(@Param("userId") Long userId);
}
