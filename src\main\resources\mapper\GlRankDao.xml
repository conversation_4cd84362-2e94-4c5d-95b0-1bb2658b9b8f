<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.dao.GlRankDao">

    <select id="getPersonalRankFirstUser" resultType="com.lvpuhui.gic.wxapp.entity.vo.RankFirstVo$RankFirstUserVo">
        SELECT
            gu.avatar_url,
            gu.nick_name
        FROM
            gl_rank gr
                INNER JOIN gl_user gu ON gr.mobile_sha256 = gu.mobile_sha256
        WHERE gr.rank > 0
        ORDER BY
            gr.rank ASC
        LIMIT 1
    </select>
</mapper>