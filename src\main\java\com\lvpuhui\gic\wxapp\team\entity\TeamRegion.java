package com.lvpuhui.gic.wxapp.team.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

/**
 * 战队地区表
 */
@Schema(description="战队地区表")
@Getter
@Setter
@TableName(value = "gl_team_region")
public class TeamRegion {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description="主键")
    @NotNull(message = "主键不能为null")
    private Long id;

    /**
     * 地区名称
     */
    @TableField(value = "region_name")
    @Schema(description="地区名称")
    @Size(max = 50,message = "地区名称最大长度要小于 50")
    private String regionName;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    @Schema(description="创建时间")
    private Date created;
}