package com.lvpuhui.gic.wxapp.team.dto;

import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 战队成员信息返回值
 * <AUTHOR>
 * @since 2023年05月24日 16:22:00
 */
@Data
public class TeamMemberRankListVo {

    /**
     * 战队ID
     */
    private Long teamId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 排行榜
     */
    private Long rank;

    /**
     * 减排量
     */
    private BigDecimal emission;

    /**
     * 减排量-文本
     */
    private String emissionText;

    /**
     * 创建人昵称
     */
    private String nickName;

    /**
     * 创建人头像
     */
    private String avatarUrl;

    public String getEmissionText() {
        if(Objects.nonNull(emission)){
            return CalcUtil.weightFormat(emission.doubleValue());
        }
        return "0g";
    }
}
