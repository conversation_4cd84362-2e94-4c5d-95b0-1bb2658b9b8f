package com.lvpuhui.gic.wxapp.team.dto;

import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 战队列表返回值
 * <AUTHOR>
 * @since 2023年05月24日 15:22:00
 */
@Data
public class TeamListVo {

    /**
     * 是否是自己的战队 true:是自己的战队  false:不是自己的战队
     */
    private Boolean isMyTeam = false;

    /**
     * 战队ID
     */
    private Long id;

    /**
     * 战队名称
     */
    private String teamName;

    /**
     * 战队Logo
     */
    private String teamLogo;

    /**
     * 成员人数
     */
    private Integer memberCount;

    /**
     * 减排量-文本
     */
    private String emissionText;

    /**
     * 减排量
     */
    private BigDecimal emission;

    /**
     * 创建人昵称
     */
    private String nickName;

    /**
     * 创建人头像
     */
    private String avatarUrl;

    /**
     * 区域code
     */
    private String regionCode;

    /**
     * 区域名称
     */
    private String regionName;

    public String getEmissionText() {
        if(Objects.nonNull(emission)){
            return CalcUtil.weightFormat(emission.doubleValue());
        }
        return "0g";
    }
}
