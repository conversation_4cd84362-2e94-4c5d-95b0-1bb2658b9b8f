package com.lvpuhui.gic.wxapp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.entity.GlVideo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * 视频表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
public interface GlVideoDao extends BaseMapper<GlVideo> {

    @Update("update gl_video set watch_count=watch_count+1 where id = #{id}")
    void updateWatchCount(@Param("id") Long id);
}
