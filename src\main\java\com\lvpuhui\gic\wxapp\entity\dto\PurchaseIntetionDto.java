package com.lvpuhui.gic.wxapp.entity.dto;


import lombok.Data;

@Data
public class PurchaseIntetionDto {


    /**
     *真实姓名
     */
    private String realName;

    /**
     *手机号
     */
    private String mobileSha256;

    /**
     * 用户公司名称
     */
    private String userCompany;

    /**
     * 行业
     */
    private String industry;

    /**
     * 地区所在地
     */
    private String zone;

    /**
     * 意向购买企业(存入企业id)
     */
    private String companyId;

    /**
     * 意向购买量
     */
    private String amount;

    /**
     * 购买用途
     */
    private String purpose;

    /**
     * 类型
     */
    private Integer operation;

}
