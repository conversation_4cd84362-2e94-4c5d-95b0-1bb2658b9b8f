package com.lvpuhui.gic.wxapp.entity.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RankInviteList {
    private Double points;
    private Double MaximumPoints;
    private InviteRankUser currentUser;
    private List<InviteRankUser> inviteRankUsers;

    @Data
    public static class InviteRankUser{

        /**
         * 排名
         */
        private Long rank;

        /**
         * 减排量
         */
        private Double emission;

        /**
         * 昵称
         */
        private String nickName;

        /**
         * 头像
         */
        private String avatarUrl;

        /**
         * 用户id(sha256)
         */
        private String mobileSha256;

        public Double getEmission() {
            if(Objects.isNull(emission) || emission < 0){
                return 0D;
            }
            return emission;
        }
    }
}
