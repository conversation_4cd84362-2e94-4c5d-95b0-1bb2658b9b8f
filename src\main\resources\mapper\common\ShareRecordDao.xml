<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.common.dao.ShareRecordDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.common.entity.ShareRecord">
    <!--@mbg.generated-->
    <!--@Table gl_share_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="mobile_sha256" jdbcType="VARCHAR" property="mobileSha256" />
    <result column="share_mobile_sha256" jdbcType="VARCHAR" property="shareMobileSha256" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `type`, source_id, mobile_sha256, share_mobile_sha256, created
  </sql>
</mapper>