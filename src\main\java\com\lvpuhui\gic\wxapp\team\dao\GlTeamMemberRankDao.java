package com.lvpuhui.gic.wxapp.team.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lvpuhui.gic.wxapp.team.dto.TeamAvgEmissionUserCountDto;
import com.lvpuhui.gic.wxapp.team.dto.TeamListDetailMemberVo;
import com.lvpuhui.gic.wxapp.team.dto.TeamMemberRankListVo;
import com.lvpuhui.gic.wxapp.team.entity.GlTeamMemberRank;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 战队成员排行表(GlTeamMemberRank)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-24 11:24:26
 */
public interface GlTeamMemberRankDao extends BaseMapper<GlTeamMemberRank> {

    /**
     * 查询战队排行队员
     * @param teamId 战队ID
     */
    List<TeamListDetailMemberVo> selectTeamRankMembers(@Param("teamId") Long teamId);

    /**
     * 查询战队人均减排量和有减排量的人数
     * @param teamId 战队ID
     */
    TeamAvgEmissionUserCountDto selectEmissionUserCount(@Param("teamId") Long teamId);

    /**
     * 查询战队贡献排行榜-前20
     */
    List<TeamMemberRankListVo> selectTeamMemberRanks(@Param("teamId") Long teamId);

    /**
     * 查询用户在战队的排行信息
     * @param userId 用户ID
     * @param teamId 战队ID
     */
    TeamMemberRankListVo selectTeamMemberRankByUserId(@Param("userId") Long userId,@Param("teamId") Long teamId);
}
