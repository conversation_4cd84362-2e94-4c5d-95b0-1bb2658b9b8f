package com.lvpuhui.gic.wxapp.team.dto;

import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 战队排行返回值
 * <AUTHOR>
 * @since 2023年05月24日 16:06:00
 */
@Data
public class TeamRankListVo {

    /**
     * 战队ID
     */
    private Long teamId;

    /**
     * 排行榜
     */
    private Long rank;

    /**
     * 减排量
     */
    private BigDecimal emission;

    /**
     * 减排量-文本
     */
    private String emissionText;

    /**
     * 战队名称
     */
    private String teamName;

    /**
     * 战队Logo
     */
    private String teamLogo;

    /**
     * 地区名称
     */
    private String regionName;

    public String getEmissionText() {
        if(Objects.nonNull(emission)){
            return CalcUtil.weightFormat(emission.doubleValue());
        }
        return "0g";
    }
}
