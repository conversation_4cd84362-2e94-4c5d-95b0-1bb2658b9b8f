<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lvpuhui.gic.wxapp.clock.dao.LowCarbonClockInDetailsDao">
  <resultMap id="BaseResultMap" type="com.lvpuhui.gic.wxapp.clock.entity.LowCarbonClockInDetails">
    <!--@mbg.generated-->
    <!--@Table gl_low_carbon_clock_in_details-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mobile_sha256" jdbcType="VARCHAR" property="mobileSha256" />
    <result column="clock_in_id" jdbcType="BIGINT" property="clockInId" />
    <result column="emission" jdbcType="DECIMAL" property="emission" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="behavior_name" jdbcType="VARCHAR" property="behaviorName" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, mobile_sha256, clock_in_id, emission, description, behavior_name, created
  </sql>

  <select id="getClockInCountGroup" resultType="com.lvpuhui.gic.wxapp.clock.dto.UserClockStatDto">
    SELECT
      clock_in_id,
      count( id ) AS clockInCount
    FROM
      gl_low_carbon_clock_in_details
    WHERE
      mobile_sha256 = #{mobileSha256}
    GROUP BY
      clock_in_id
  </select>

  <select id="getCountByMobileSha256" resultType="int">
    SELECT
      count( id )
    FROM
      gl_low_carbon_clock_in_details
    WHERE
      mobile_sha256 = #{mobileSha256}
  </select>
</mapper>